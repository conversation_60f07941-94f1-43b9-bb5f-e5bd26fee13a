{extend name="layout/base" /}

{block name="title"}个人资料 - 卡密兑换管理系统{/block}

{block name="content"}
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 24px;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    /* 个人资料卡片 */
    .profile-container {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 24px;
        max-width: 1200px;
    }

    .profile-sidebar {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        height: fit-content;
    }

    .profile-avatar {
        text-align: center;
        margin-bottom: 24px;
    }

    .avatar-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), #40a9ff);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: 600;
        margin: 0 auto 16px;
    }

    .profile-name {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .profile-username {
        color: var(--text-secondary);
        font-size: 14px;
    }

    .profile-stats {
        margin-top: 24px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .stat-item:last-child {
        border-bottom: none;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 14px;
    }

    .stat-value {
        color: var(--text-primary);
        font-weight: 500;
        font-size: 14px;
    }

    /* 主内容区 */
    .profile-main {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .profile-header {
        padding: 24px;
        border-bottom: 1px solid #f0f0f0;
        background: #f8f9fa;
    }

    .profile-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .profile-body {
        padding: 24px;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-control:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
    }

    .btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
    }

    /* 密码修改区域 */
    .password-section {
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
    }

    .password-section h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 16px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .profile-container {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .profile-sidebar {
            order: 2;
        }
        
        .profile-main {
            order: 1;
        }
    }
</style>

<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">个人资料</h1>
    <p class="page-subtitle">管理您的个人信息和账户设置</p>
</div>

<div class="profile-container">
    <!-- 侧边栏 -->
    <div class="profile-sidebar">
        <div class="profile-avatar">
            <div class="avatar-circle">
                {$admin.name|substr=0,1|upper}
            </div>
            <div class="profile-name">{$admin.name}</div>
            <div class="profile-username">@{$admin.username}</div>
        </div>
        
        <div class="profile-stats">
            <div class="stat-item">
                <span class="stat-label">账户状态</span>
                <span class="stat-value">{$stats.status}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">创建时间</span>
                <span class="stat-value">{$stats.created_at|date='Y-m-d'}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">{$stats.last_login_time|date='m-d H:i'}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">登录IP</span>
                <span class="stat-value">{$stats.last_login_ip}</span>
            </div>
        </div>
    </div>
    
    <!-- 主内容 -->
    <div class="profile-main">
        <div class="profile-header">
            <h3>基本信息</h3>
        </div>
        
        <div class="profile-body">
            <form id="profileForm">
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" name="name" class="form-control" value="{$admin.name}" required>
                    <div class="form-text">您的真实姓名或显示名称</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" value="{$admin.username}" disabled>
                    <div class="form-text">用户名不可修改，如需修改请联系系统管理员</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" name="email" class="form-control" value="{$admin.email}">
                    <div class="form-text">用于接收系统通知和找回密码</div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存更改
                    </button>
                    <button type="button" class="btn btn-outline" onclick="resetForm()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </form>
            
            <!-- 密码修改区域 -->
            <div class="password-section">
                <h4>修改密码</h4>
                <form id="passwordForm">
                    <div class="form-group">
                        <label class="form-label">当前密码</label>
                        <input type="password" name="current_password" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">新密码</label>
                        <input type="password" name="new_password" class="form-control" minlength="6" required>
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">确认新密码</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i>
                            修改密码
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // 个人资料表单提交
    document.getElementById('profileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        fetch('/profile/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                // 更新页面显示的用户名
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('更新失败，请稍后重试', 'error');
        });
    });

    // 密码修改表单提交
    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // 验证密码确认
        if (data.new_password !== data.confirm_password) {
            showToast('两次输入的密码不一致', 'error');
            return;
        }
        
        fetch('/profile/changePassword', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                // 清空表单
                document.getElementById('passwordForm').reset();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('密码修改失败，请稍后重试', 'error');
        });
    });

    // 重置表单
    function resetForm() {
        location.reload();
    }

    // Toast 通知
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }
</script>

<!-- Toast 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
</style>

{/block}
