-- 插入测试分类数据
-- 先清空现有数据
DELETE FROM `km_categories`;

-- 插入一级分类
INSERT INTO `km_categories` (`id`, `parent_id`, `name`, `description`, `status`, `sort_order`, `level`, `path`, `created_at`, `updated_at`) VALUES
(1, 0, '会员卡密', '各种会员等级的卡密分类', 1, 10, 1, '1', NOW(), NOW()),
(2, 0, '软件授权', '软件产品授权卡密', 1, 20, 1, '2', NOW(), NOW()),
(3, 0, '游戏道具', '游戏相关道具和装备', 1, 30, 1, '3', NOW(), NOW()),
(4, 0, '教育资源', '教育培训相关资源', 1, 40, 1, '4', NOW(), NOW()),
(5, 0, '数字内容', '数字化内容产品', 1, 50, 1, '5', NOW(), NOW());

-- 插入二级分类
INSERT INTO `km_categories` (`id`, `parent_id`, `name`, `description`, `status`, `sort_order`, `level`, `path`, `created_at`, `updated_at`) VALUES
-- 会员卡密的子分类
(11, 1, '基础会员', '基础会员权限，30天有效期', 1, 10, 2, '1,11', NOW(), NOW()),
(12, 1, '高级会员', '高级会员权限，90天有效期', 1, 20, 2, '1,12', NOW(), NOW()),
(13, 1, '年度会员', '年度会员权限，365天有效期', 1, 30, 2, '1,13', NOW(), NOW()),
(14, 1, '终身会员', '终身会员权限，永久有效', 1, 40, 2, '1,14', NOW(), NOW()),

-- 软件授权的子分类
(21, 2, 'Office套件', 'Microsoft Office等办公软件', 1, 10, 2, '2,21', NOW(), NOW()),
(22, 2, '设计软件', 'Photoshop、AI等设计软件', 1, 20, 2, '2,22', NOW(), NOW()),
(23, 2, '开发工具', '编程开发相关工具软件', 1, 30, 2, '2,23', NOW(), NOW()),
(24, 2, '安全软件', '杀毒软件、防火墙等安全工具', 1, 40, 2, '2,24', NOW(), NOW()),

-- 游戏道具的子分类
(31, 3, '装备道具', '游戏装备和道具', 1, 10, 2, '3,31', NOW(), NOW()),
(32, 3, '游戏币', '各种游戏内货币', 1, 20, 2, '3,32', NOW(), NOW()),
(33, 3, '皮肤外观', '游戏角色皮肤和外观', 1, 30, 2, '3,33', NOW(), NOW()),

-- 教育资源的子分类
(41, 4, '在线课程', '各种在线教育课程', 1, 10, 2, '4,41', NOW(), NOW()),
(42, 4, '学习资料', '学习资料和电子书', 1, 20, 2, '4,42', NOW(), NOW()),

-- 数字内容的子分类
(51, 5, '音乐资源', '音乐和音频内容', 1, 10, 2, '5,51', NOW(), NOW()),
(52, 5, '视频内容', '视频和影视内容', 1, 20, 2, '5,52', NOW(), NOW());
