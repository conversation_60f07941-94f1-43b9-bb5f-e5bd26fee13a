<?php /*a:2:{s:46:"F:\linshi\thphp\kmxt\view\dashboard\index.html";i:1753934671;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1753955066;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --sidebar-bg: #001529;
            --sidebar-text: rgba(255, 255, 255, 0.65);
            --sidebar-active: #1890ff;
            --content-bg: #f0f2f5;
            --card-bg: #ffffff;
            --border-color: #d9d9d9;
            --text-primary: #262626;
            --text-secondary: #8c8c8c;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
            background-color: var(--content-bg);
            padding: 24px;
        }

        .main-content.expanded {
            margin-left: 0;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<style>
    /* 统计卡片样式 */
    .stats-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin-bottom: 16px;
        position: relative;
        z-index: 2;
    }
    
    .stats-value {
        font-size: 32px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
        line-height: 1;
    }

    .stats-label {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .stats-growth {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
    }
    
    .growth-positive {
        color: var(--success-color);
    }

    .growth-negative {
        color: var(--error-color);
    }

    /* 图标颜色 */
    .icon-primary { background-color: var(--primary-color); }
    .icon-success { background-color: var(--success-color); }
    .icon-warning { background-color: var(--warning-color); }
    .icon-info { background-color: #13c2c2; }
    .icon-purple { background-color: #722ed1; }
    
    /* 图表容器样式 */
    .chart-container {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .chart-controls {
        display: flex;
        gap: 0.5rem;
    }
    
    .chart-controls .btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* 表格样式 */
    .records-table {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #eee;
    }
    
    .table-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    
    .table td {
        border: none;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }
    
    .table tbody tr {
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-used {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-unused {
        background-color: #fff3cd;
        color: #856404;
    }
    
    /* 快速操作样式 */
    .quick-actions {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s ease;
        background: var(--card-bg);
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .action-item:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
        text-decoration: none;
    }
    
    .action-icon {
        font-size: 28px;
        margin-bottom: 12px;
        color: var(--text-secondary);
        transition: color 0.3s ease;
    }

    .action-item:hover .action-icon {
        color: var(--primary-color);
    }

    .action-label {
        font-size: 14px;
        font-weight: 500;
        text-align: center;
    }
    
    /* 系统状态样式 */
    .system-status {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .status-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .status-success {
        background-color: #28a745;
    }
    
    .status-warning {
        background-color: #ffc107;
    }
    
    .status-danger {
        background-color: #dc3545;
    }
    
    .status-text {
        font-size: 0.9rem;
        color: #495057;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .chart-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .system-status {
            grid-template-columns: 1fr;
        }
    }
</style>

</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                <a href="/logs" class="nav-link">
                    <i class="fas fa-list-alt"></i>
                    操作日志
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<div class="page-title">
    <h1>控制台</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active">控制台</li>
        </ol>
    </nav>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon icon-primary">
                <i class="<?php echo htmlentities((string) $cardStats['total_cards']['icon']); ?>"></i>
            </div>
            <div class="stats-value"><?php echo htmlentities((string) $cardStats['total_cards']['value']); ?></div>
            <div class="stats-label">总卡密数</div>
            <div class="stats-growth <?php echo $cardStats['total_cards']['growth']>=0 ? 'growth-positive'  :  'growth-negative'; ?>">
                <i class="fas fa-arrow-<?php echo $cardStats['total_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
                <?php echo htmlentities((string) $cardStats['total_cards']['growth']); ?>%
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon icon-success">
                <i class="<?php echo htmlentities((string) $cardStats['used_cards']['icon']); ?>"></i>
            </div>
            <div class="stats-value"><?php echo htmlentities((string) $cardStats['used_cards']['value']); ?></div>
            <div class="stats-label">已使用卡密</div>
            <div class="stats-growth <?php echo $cardStats['used_cards']['growth']>=0 ? 'growth-positive'  :  'growth-negative'; ?>">
                <i class="fas fa-arrow-<?php echo $cardStats['used_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
                <?php echo htmlentities((string) $cardStats['used_cards']['growth']); ?>%
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background-color: var(--error-color);">
                <i class="<?php echo htmlentities((string) $cardStats['disabled_cards']['icon']); ?>"></i>
            </div>
            <div class="stats-value"><?php echo htmlentities((string) $cardStats['disabled_cards']['value']); ?></div>
            <div class="stats-label">已禁用卡密</div>
            <div class="stats-growth <?php echo $cardStats['disabled_cards']['growth']>=0 ? 'growth-positive'  :  'growth-negative'; ?>">
                <i class="fas fa-arrow-<?php echo $cardStats['disabled_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
                <?php echo htmlentities((string) $cardStats['disabled_cards']['growth']); ?>%
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon icon-warning">
                <i class="<?php echo htmlentities((string) $cardStats['unused_cards']['icon']); ?>"></i>
            </div>
            <div class="stats-value"><?php echo htmlentities((string) $cardStats['unused_cards']['value']); ?></div>
            <div class="stats-label">未使用卡密</div>
            <div class="stats-growth <?php echo $cardStats['unused_cards']['growth']>=0 ? 'growth-positive'  :  'growth-negative'; ?>">
                <i class="fas fa-arrow-<?php echo $cardStats['unused_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
                <?php echo htmlentities((string) $cardStats['unused_cards']['growth']); ?>%
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon icon-info">
                <i class="<?php echo htmlentities((string) $cardStats['category_count']['icon']); ?>"></i>
            </div>
            <div class="stats-value"><?php echo htmlentities((string) $cardStats['category_count']['value']); ?></div>
            <div class="stats-label">分类总数</div>
            <div class="stats-growth <?php echo $cardStats['category_count']['growth']>=0 ? 'growth-positive'  :  'growth-negative'; ?>">
                <i class="fas fa-arrow-<?php echo $cardStats['category_count']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
                <?php echo htmlentities((string) $cardStats['category_count']['growth']); ?>%
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 使用趋势图表 -->
    <div class="col-lg-8 mb-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="day">日</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="week">周</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="month">月</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="year">年</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageTrendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 分类占比图表 -->
    <div class="col-lg-4 mb-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">分类占比</h5>
            </div>
            <div style="height: 300px;">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近使用记录和快速操作 -->
<div class="row">
    <!-- 最近使用记录 -->
    <div class="col-lg-8 mb-4">
        <div class="records-table">
            <div class="table-header">
                <h5 class="table-title">最近使用记录</h5>
                <a href="/usage-records" class="btn btn-outline-primary btn-sm">查看全部</a>
            </div>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密编号</th>
                            <th>分类</th>
                            <th>使用时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(is_array($recentRecords) || $recentRecords instanceof \think\Collection || $recentRecords instanceof \think\Paginator): $i = 0; $__LIST__ = $recentRecords;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$record): $mod = ($i % 2 );++$i;?>
                        <tr>
                            <td><code><?php echo htmlentities((string) $record['masked_card_code']); ?></code></td>
                            <td><?php echo htmlentities((string) $record['category_name']); ?></td>
                            <td><?php echo htmlentities((string) $record['used_at']); ?></td>
                            <td><span class="status-badge status-used">已使用</span></td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; if(empty($recentRecords) || (($recentRecords instanceof \think\Collection || $recentRecords instanceof \think\Paginator ) && $recentRecords->isEmpty())): ?>
                        <tr>
                            <td colspan="4" class="text-center text-muted py-4">暂无使用记录</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 快速操作中心 -->
    <div class="col-lg-4 mb-4">
        <div class="quick-actions">
            <div class="table-header">
                <h5 class="table-title">快速操作</h5>
            </div>

            <div class="actions-grid">
                <a href="/generate" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="action-label">生成卡密</div>
                </a>

                <a href="/cards" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="action-label">卡密管理</div>
                </a>

                <a href="/categories" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="action-label">分类管理</div>
                </a>

                <a href="/content" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="action-label">内容管理</div>
                </a>

                <a href="#" class="action-item" onclick="showExportModal()">
                    <div class="action-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="action-label">导出数据</div>
                </a>

                <a href="/settings" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="action-label">系统设置</div>
                </a>
            </div>

            <!-- 系统状态监控 -->
            <div class="table-header">
                <h6 class="table-title">系统状态</h6>
            </div>
            <div class="system-status">
                <div class="status-item">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['server_status']['color']); ?>"></div>
                    <div class="status-text">
                        <strong>服务器状态</strong><br>
                        <small><?php echo htmlentities((string) $systemStatus['server_status']['text']); ?></small>
                    </div>
                </div>

                <div class="status-item">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['database_status']['color']); ?>"></div>
                    <div class="status-text">
                        <strong>数据库连接</strong><br>
                        <small><?php echo htmlentities((string) $systemStatus['database_status']['text']); ?></small>
                    </div>
                </div>

                <div class="status-item">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['storage_status']['color']); ?>"></div>
                    <div class="status-text">
                        <strong>存储空间</strong><br>
                        <small><?php echo htmlentities((string) $systemStatus['storage_status']['text']); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportType" class="form-label">导出类型</label>
                        <select class="form-select" id="exportType">
                            <option value="all">全部数据</option>
                            <option value="cards">卡密数据</option>
                            <option value="usage">使用记录</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportData()">导出</button>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // 点击外部关闭移动端侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('expanded');
                } else {
                    mainContent.classList.remove('expanded');
                }
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });

        // 设置导航激活状态
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    
</body>
</html>
