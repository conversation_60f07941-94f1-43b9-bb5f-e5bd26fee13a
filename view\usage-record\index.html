{extend name="layout/base" /}

{block name="title"}使用记录 - 卡密兑换管理系统{/block}

{block name="content"}
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 24px;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    /* 统计卡片 */
    .stats-row {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* 筛选区域 */
    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
    }

    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        align-items: end;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }

    .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
    }

    .btn-success {
        background: var(--success-color);
        color: white;
    }

    .btn-success:hover {
        background: #389e0d;
        color: white;
        text-decoration: none;
    }

    /* 表格样式 */
    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .table-header {
        padding: 16px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .table {
        width: 100%;
        margin: 0;
        border-collapse: collapse;
    }

    .table th {
        background: #f8f9fa;
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;
    }

    .table td {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;
        color: var(--text-primary);
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* 状态标签 */
    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        background: var(--success-color);
        color: white;
    }

    /* 分页样式 */
    .pagination-wrapper {
        padding: 16px 20px;
        background: #f8f9fa;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pagination-info {
        font-size: 14px;
        color: var(--text-secondary);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .stats-row {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .filter-row {
            grid-template-columns: 1fr;
        }
        
        .table-header {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }
    }
</style>

<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">使用记录</h1>
    <p class="page-subtitle">查看和管理卡密使用记录</p>
</div>

<!-- 统计数据 -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-value">{$stats.total_usage}</div>
        <div class="stat-label">总使用次数</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{$stats.today_usage}</div>
        <div class="stat-label">今日使用</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{$stats.week_usage}</div>
        <div class="stat-label">本周使用</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{$stats.month_usage}</div>
        <div class="stat-label">本月使用</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{$stats.unique_ips}</div>
        <div class="stat-label">活跃IP数</div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <form method="get" action="/usage-records">
        <div class="filter-row">
            <div class="form-group">
                <label class="form-label">分类</label>
                <select name="category_id" class="form-control">
                    <option value="">全部分类</option>
                    {volist name="categories" id="category"}
                    <option value="{$category.id}" {$filters.category_id == $category.id ? 'selected' : ''}>{$category.name}</option>
                    {/volist}
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">卡密编号</label>
                <input type="text" name="keyword" class="form-control" placeholder="搜索卡密编号" value="{$filters.keyword}">
            </div>
            
            <div class="form-group">
                <label class="form-label">开始日期</label>
                <input type="date" name="start_date" class="form-control" value="{$filters.start_date}">
            </div>
            
            <div class="form-group">
                <label class="form-label">结束日期</label>
                <input type="date" name="end_date" class="form-control" value="{$filters.end_date}">
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="/usage-records" class="btn btn-outline">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 使用记录表格 -->
<div class="table-container">
    <div class="table-header">
        <h5 class="table-title">使用记录列表</h5>
        <div>
            <a href="/usage-records/export{$exportUrl}" class="btn btn-success">
                <i class="fas fa-download"></i>
                导出记录
            </a>
        </div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>卡密编号</th>
                <th>分类</th>
                <th>使用IP</th>
                <th>使用时间</th>
                <th>状态</th>
            </tr>
        </thead>
        <tbody>
            {volist name="records" id="record"}
            <tr>
                <td><code>{$record.card_code}</code></td>
                <td>{$record.category_name|default='未分类'}</td>
                <td>{$record.used_ip|default='未知'}</td>
                <td>{$record.used_at}</td>
                <td><span class="status-badge">已使用</span></td>
            </tr>
            {/volist}
            {empty name="records"}
            <tr>
                <td colspan="5" class="text-center" style="padding: 40px; color: #8c8c8c;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                    <div>暂无使用记录</div>
                </td>
            </tr>
            {/empty}
        </tbody>
    </table>
    
    <!-- 分页 -->
    {if condition="$records->hasPages()"}
    <div class="pagination-wrapper">
        <div class="pagination-info">
            显示第 {$records->currentPage()} 页，共 {$records->lastPage()} 页，总计 {$records->total()} 条记录
        </div>
        <div>
            {$records->render()|raw}
        </div>
    </div>
    {/if}
</div>

{/block}
