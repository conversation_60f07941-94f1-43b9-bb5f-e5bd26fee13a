-- 插入基础数据（简化版）

-- 插入分类数据
INSERT INTO `km_categories` (`name`, `description`, `sort_order`, `status`) VALUES
('基础会员', '基础会员卡密，提供基本功能', 0, 1),
('高级会员', '高级会员卡密，提供高级功能', 1, 1),
('年度会员', '年度会员卡密，一年有效期', 2, 1),
('终身会员', '终身会员卡密，永久有效', 3, 1),
('企业版', '企业版卡密，适合团队使用', 4, 1);

-- 插入示例卡密数据
INSERT INTO `km_cards` (`card_code`, `category_id`, `status`, `content`, `used_at`, `used_ip`, `expire_at`) VALUES
('BASIC001234567890', 1, 1, '基础会员权限：30天有效期', '2024-07-25 10:30:00', '*************', '2024-08-25 23:59:59'),
('BASIC001234567891', 1, 0, '基础会员权限：30天有效期', NULL, NULL, '2024-08-31 23:59:59'),
('BASIC001234567892', 1, 0, '基础会员权限：30天有效期', NULL, NULL, '2024-08-31 23:59:59'),
('BASIC001234567893', 1, 2, '基础会员权限：30天有效期', NULL, NULL, '2024-08-31 23:59:59'),
('PREMIUM0123456789', 2, 1, '高级会员权限：90天有效期', '2024-07-26 14:20:00', '*************', '2024-10-26 23:59:59'),
('PREMIUM0123456790', 2, 1, '高级会员权限：90天有效期', '2024-07-27 09:15:00', '*************', '2024-10-27 23:59:59'),
('PREMIUM0123456791', 2, 0, '高级会员权限：90天有效期', NULL, NULL, '2024-10-31 23:59:59'),
('PREMIUM0123456792', 2, 0, '高级会员权限：90天有效期', NULL, NULL, '2024-10-31 23:59:59'),
('YEARLY01234567890', 3, 1, '年度会员权限：365天有效期', '2024-07-28 16:45:00', '***********03', '2025-07-28 23:59:59'),
('YEARLY01234567891', 3, 0, '年度会员权限：365天有效期', NULL, NULL, '2025-07-31 23:59:59'),
('LIFETIME123456789', 4, 1, '终身会员权限：永久有效', '2024-07-29 11:30:00', '***********04', NULL),
('LIFETIME123456790', 4, 0, '终身会员权限：永久有效', NULL, NULL, NULL),
('ENTERPRISE1234567', 5, 0, '企业版权限：支持多用户', NULL, NULL, '2024-12-31 23:59:59'),
('ENTERPRISE1234568', 5, 0, '企业版权限：支持多用户', NULL, NULL, '2024-12-31 23:59:59'),
('ENTERPRISE1234569', 5, 2, '企业版权限：支持多用户', NULL, NULL, '2024-12-31 23:59:59');

-- 插入使用记录数据
INSERT INTO `km_usage_records` (`card_id`, `card_code`, `category_id`, `used_ip`, `user_agent`, `used_at`) VALUES
(1, 'BASIC001234567890', 1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-07-25 10:30:00'),
(5, 'PREMIUM0123456789', 2, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', '2024-07-26 14:20:00'),
(6, 'PREMIUM0123456790', 2, '*************', 'Mozilla/5.0 (X11; Linux x86_64)', '2024-07-27 09:15:00'),
(9, 'YEARLY01234567890', 3, '***********03', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-07-28 16:45:00'),
(11, 'LIFETIME123456789', 4, '***********04', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)', '2024-07-29 11:30:00');

-- 更新系统设置
UPDATE `km_system_settings` SET `setting_value` = '123456' WHERE `setting_key` = 'export_password';
UPDATE `km_system_settings` SET `setting_value` = '卡密兑换管理系统 v1.0' WHERE `setting_key` = 'site_title';

-- 插入更多系统设置
INSERT INTO `km_system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('auto_disable_expired', '1', 'bool', '自动禁用过期卡密'),
('notification_email', '<EMAIL>', 'string', '通知邮箱地址'),
('max_usage_per_ip', '5', 'int', '每个IP每日最大使用次数'),
('enable_usage_log', '1', 'bool', '启用使用日志记录'),
('card_code_length', '16', 'int', '卡密编号长度'),
('default_expire_days', '30', 'int', '默认过期天数');

-- 插入操作日志示例
INSERT INTO `km_operation_logs` (`admin_id`, `operation_type`, `operation_desc`, `request_data`, `ip_address`, `user_agent`) VALUES
(1, 'card_generate', '批量生成卡密', '{"category_id":1,"count":10}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(1, 'card_disable', '禁用卡密', '{"card_id":4}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(1, 'category_create', '创建分类', '{"name":"企业版"}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(1, 'settings_update', '更新系统设置', '{"export_password":"******"}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'),
(1, 'data_export', '导出数据', '{"type":"all"}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)');
