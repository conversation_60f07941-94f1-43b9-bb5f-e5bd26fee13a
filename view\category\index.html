{extend name="layout/base" /}

{block name="title"}分类管理 - 卡密兑换管理系统{/block}

{block name="style"}
<style>
    .page-header {
        margin-bottom: 32px;
    }

    .page-title-main {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .header-btn.btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .header-btn.btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .header-btn.btn-success:hover {
        background: #218838;
        border-color: #218838;
        color: white;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
    }

    .header-btn.btn-warning {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .header-btn.btn-warning:hover {
        background: #5a6268;
        border-color: #5a6268;
        color: white;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25);
    }

    .header-btn i {
        font-size: 11px;
    }

    /* 统计卡片 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 110px;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--accent-color);
        border-radius: 16px 16px 0 0;
    }

    .stat-card.blue::before {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
    }

    .stat-card.green::before {
        background: linear-gradient(135deg, #52c41a, #73d13d);
    }

    .stat-card.purple::before {
        background: linear-gradient(135deg, #722ed1, #9254de);
    }

    .stat-card.orange::before {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
    }

    .stat-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .stat-icon.blue {
        background: linear-gradient(135deg, #e6f7ff, #bae7ff);
        color: #1890ff;
    }

    .stat-icon.green {
        background: linear-gradient(135deg, #f6ffed, #d9f7be);
        color: #52c41a;
    }

    .stat-icon.purple {
        background: linear-gradient(135deg, #f9f0ff, #efdbff);
        color: #722ed1;
    }

    .stat-icon.orange {
        background: linear-gradient(135deg, #fff7e6, #ffd591);
        color: #fa8c16;
    }

    .stat-label {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #262626;
        line-height: 1;
        margin: 0;
    }

    /* 分类列表卡片 */
    .category-list-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .category-list-header {
        padding: 24px 24px 0 24px;
        border-bottom: none;
    }

    .category-list-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .category-list-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0 0 24px 0;
    }

    /* 分类表格样式 */
    .category-table {
        width: 100%;
        border-collapse: collapse;
    }

    .category-table th {
        background: #fafafa;
        padding: 16px 20px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 2px solid #f0f0f0;
        font-size: 14px;
    }

    .category-table td {
        padding: 16px 20px;
        border-bottom: 1px solid #f5f5f5;
        vertical-align: middle;
        font-size: 14px;
    }

    .category-row {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .category-row:hover {
        background: #fafafa;
    }

    .category-row.selected {
        background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        position: relative;
    }



    .category-row.level-1 {
        background: #f9f9f9;
    }

    .category-row.level-2 {
        background: #fdfdfd;
    }

    .category-row.level-3 {
        background: white;
    }

    .category-row.level-1:hover:not(.selected) {
        background: #f0f0f0;
    }

    .category-row.level-2:hover:not(.selected) {
        background: #f8f8f8;
    }

    .category-row.level-3:hover:not(.selected) {
        background: #fafafa;
    }

    /* 选中状态覆盖所有层级的背景色 */
    .category-row.selected.level-1,
    .category-row.selected.level-2,
    .category-row.selected.level-3 {
        background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%) !important;
    }

    /* 分类名称列 */
    .category-name-cell {
        display: flex;
        align-items: center;
    }

    /* 选中状态的小蓝点 */
    .category-row.selected .category-name-text::after {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        background: var(--primary-color);
        border-radius: 50%;
        margin-left: 8px;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        animation: selectedPulse 2s infinite;
        vertical-align: middle;
    }

    @keyframes selectedPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.2);
        }
    }

    .expand-arrow {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        color: #8c8c8c;
        transition: transform 0.2s ease;
        font-size: 10px;
        cursor: pointer;
        border-radius: 2px;
    }

    .expand-arrow:hover {
        background: rgba(0,0,0,0.04);
    }

    .expand-arrow.expanded {
        transform: rotate(90deg);
    }

    .expand-arrow.no-children {
        visibility: hidden;
    }

    .category-icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 12px;
        flex-shrink: 0;
    }

    .category-icon.level-1 {
        background: #e6f7ff;
        color: #1890ff;
    }

    .category-icon.level-2 {
        background: #f6ffed;
        color: #52c41a;
    }

    .category-icon.level-3 {
        background: #f9f0ff;
        color: #722ed1;
    }

    .category-name {
        font-weight: 500;
        color: var(--text-primary);
    }

    .category-row.level-1 .category-name {
        font-weight: 600;
    }

    .category-row.level-2 .category-name-cell {
        padding-left: 20px;
    }

    .category-row.level-3 .category-name-cell {
        padding-left: 40px;
    }

    /* 状态标签 */
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-enabled {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #d9f7be;
    }

    .status-disabled {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
    }

    /* 操作按钮 */
    .category-actions {
        display: flex;
        align-items: center;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .category-row:hover .category-actions {
        opacity: 1;
    }

    .category-row.selected .category-actions {
        opacity: 1;
    }

    .action-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background: var(--primary-color);
        color: white;
    }

    .action-btn.edit:hover {
        background: #52c41a;
    }

    .action-btn.delete:hover {
        background: #ff4d4f;
    }

    .action-btn.toggle:hover {
        background: #fa8c16;
    }

    /* 子分类默认隐藏 */
    .category-child {
        display: none;
    }

    .badge {
        font-size: 11px;
        padding: 4px 8px;
    }

    /* ID列样式 */
    .category-id {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
        color: var(--text-secondary);
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
        min-width: 30px;
        text-align: center;
    }





    /* 排序控制样式 */
    .sort-control {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sort-input {
        width: 60px;
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        text-align: center;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .sort-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        outline: none;
    }

    .sort-input:hover {
        border-color: var(--primary-color);
    }

    .sort-input.changed {
        background-color: #fff7e6;
        border-color: #ffa940;
    }

    /* 分类列表头部样式 */
    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
    }

    .header-left {
        flex: 1;
    }

    .header-right {
        flex-shrink: 0;
    }

    .help-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        color: #495057;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .help-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #212529;
        transform: translateY(-1px);
    }

    .help-btn i {
        font-size: 16px;
        color: var(--primary-color);
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 16px;
        width: 90%;
        max-width: 800px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .modal-header h2 i {
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 32px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .help-section {
        margin-bottom: 32px;
    }

    .help-section:last-child {
        margin-bottom: 0;
    }

    .help-section h3 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .help-section h3 i {
        color: var(--primary-color);
        font-size: 20px;
    }

    .hierarchy-levels {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .level-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .level-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 28px;
        border-radius: 14px;
        font-size: 12px;
        font-weight: 700;
        color: white;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .level-badge.level-1 {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
    }

    .level-badge.level-2 {
        background: linear-gradient(135deg, #52c41a, #73d13d);
    }

    .level-badge.level-3 {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
    }

    .level-content {
        flex: 1;
    }

    .level-desc {
        display: block;
        font-size: 15px;
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 6px;
    }

    .level-example {
        display: block;
        font-size: 13px;
        color: #6c757d;
        font-style: italic;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .info-item i {
        font-size: 20px;
        margin-top: 2px;
        flex-shrink: 0;
    }

    .info-item strong {
        display: block;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .info-item p {
        margin: 0;
        font-size: 13px;
        color: #6c757d;
        line-height: 1.4;
    }

    .modal-footer {
        padding: 20px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
    }

    .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modal-content {
            margin: 10% auto;
            width: 95%;
            max-height: 85vh;
        }

        .modal-header {
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
        }

        .header-content {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
        }

        .help-btn {
            align-self: flex-start;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .level-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
    }

    @keyframes flash-success {
        0% { background-color: inherit; }
        50% { background-color: #f6ffed; border-left: 4px solid #52c41a; }
        100% { background-color: inherit; }
    }



    .category-row.flash-success {
        animation: flash-success 1s ease;
    }

    /* 分类名称可点击样式 */
    .category-name-clickable {
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        flex: 1;
        padding: 4px 0;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    .category-name-clickable:hover {
        background: rgba(24, 144, 255, 0.08);
    }

    .category-name-text {
        margin-left: 4px;
    }

    /* 动画效果 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 拖拽时的视觉效果 */
    .category-row.dragging {
        opacity: 0.5;
    }

    .category-row.drag-over::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--primary-color);
        z-index: 10;
    }

    /* 子分类容器 */
    .category-children {
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .category-children.collapsed {
        max-height: 0;
        opacity: 0;
    }

    .category-children.expanded {
        max-height: 2000px;
        opacity: 1;
    }

    /* 状态指示器 */
    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .status-dot.enabled {
        background: #52c41a;
    }

    .status-dot.disabled {
        background: #ff4d4f;
    }

    /* 操作按钮 */
    .tree-actions {
        display: none;
        margin-left: 8px;
        gap: 4px;
    }

    .tree-item:hover .tree-actions {
        display: flex;
    }

    .tree-item.selected .tree-actions {
        display: flex;
    }

    .action-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        color: inherit;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .tree-item:not(.selected) .action-btn {
        background: rgba(0, 0, 0, 0.05);
        color: #8c8c8c;
    }

    .tree-item:not(.selected) .action-btn:hover {
        background: rgba(0, 0, 0, 0.1);
        color: var(--primary-color);
    }

    /* 状态指示器 */
    .status-indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .status-indicator.enabled {
        background: #52c41a;
    }

    .status-indicator.disabled {
        background: #ff4d4f;
    }

    /* 空状态 */
    .empty-tree {
        text-align: center;
        padding: 60px 20px;
        color: #8c8c8c;
    }

    .empty-tree i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #d9d9d9;
    }

    /* 详情面板 */
    .category-details {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .details-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .details-content {
        display: none;
    }

    .details-content.show {
        display: block;
    }

    .detail-item {
        display: flex;
        margin-bottom: 12px;
    }

    .detail-label {
        width: 80px;
        color: var(--text-secondary);
        font-size: 14px;
        flex-shrink: 0;
    }

    .detail-value {
        flex: 1;
        color: var(--text-primary);
        font-size: 14px;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-enabled {
        background-color: #f6ffed;
        color: #389e0d;
        border: 1px solid #b7eb8f;
    }

    .status-disabled {
        background-color: #fff2f0;
        color: #cf1322;
        border: 1px solid #ffccc7;
    }

    @media (max-width: 768px) {
        .category-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .btn-group {
            flex-wrap: wrap;
        }

        .tree-item.level-2 {
            padding-left: 28px;
        }

        .tree-item.level-3 {
            padding-left: 44px;
        }
    }
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">分类管理</h1>
        <p class="text-muted mb-0">管理和配置分类层级结构</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="expandAll()">
            <i class="fas fa-expand-arrows-alt"></i>
            全部展开
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="collapseAll()">
            <i class="fas fa-compress-arrows-alt"></i>
            全部收起
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="importCategories()">
            <i class="fas fa-file-import"></i>
            导入分类
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="exportCategories()">
            <i class="fas fa-file-export"></i>
            导出分类
        </button>
        <a href="/categories/add" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            新建分类
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">
            {php}
                echo \app\model\Category::where('parent_id', 0)->where('status', 1)->count();
            {/php}
        </div>
        <div class="modern-stats-label">顶级分类</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-sitemap"></i>
        </div>
        <div class="modern-stats-value">
            {php}
                echo \app\model\Category::where('status', 1)->count();
            {/php}
        </div>
        <div class="modern-stats-label">分类总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="fas fa-tags"></i>
        </div>
        <div class="modern-stats-value">
            {php}
                echo \app\model\Card::count();
            {/php}
        </div>
        <div class="modern-stats-label">卡密总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">3</div>
        <div class="modern-stats-label">最大层级</div>
    </div>
</div>

<!-- 分类列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <div class="d-flex justify-content-between align-items-center w-100">
            <div>
                <h5 class="modern-card-title">
                    <i class="fas fa-sitemap me-2"></i>
                    分类层级结构
                </h5>
                <small class="text-muted">点击箭头或分类名展开/收起子分类，悬停显示操作按钮</small>
            </div>
            <button class="modern-btn modern-btn-outline btn-sm" onclick="showHierarchyHelp()">
                <i class="fas fa-question-circle"></i>
                层级说明
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">

        {empty name="categories"}
        <div class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-folder-open fa-3x mb-3 opacity-25"></i>
                <div class="h5">暂无分类数据</div>
                <p>开始创建您的第一个分类吧</p>
                <a href="/categories/add" class="modern-btn modern-btn-primary">
                    <i class="fas fa-plus"></i>
                    添加分类
                </a>
            </div>
        </div>
        {else}
        <div class="modern-table-container">
            <table class="modern-table category-table">
        <thead>
            <tr>
                <th width="8%">ID</th>
                <th width="35%">分类名称</th>
                <th width="20%">描述</th>
                <th width="8%">排序</th>
                <th width="8%">状态</th>
                <th width="8%">子分类</th>
                <th width="13%">操作</th>
            </tr>
        </thead>
        <tbody>
            {volist name="categoryTree" id="category"}
            <!-- 一级分类 -->
            <tr class="category-row level-1"
                data-id="{$category.id}"
                data-level="1"
                data-parent="0"
                data-sort="{$category.sort_order}"
                onclick="selectCategory({$category.id})">

                <td>
                    <span class="category-id">{$category.id}</span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow {if empty($category.children)}no-children{/if}"
                             onclick="event.stopPropagation(); toggleCategory({$category.id})">
                            {if !empty($category.children)}
                            <i class="fas fa-chevron-right"></i>
                            {/if}
                        </div>

                        <div class="category-icon level-1">
                            <i class="fas fa-cube"></i>
                        </div>

                        <div class="category-name-clickable" onclick="event.stopPropagation(); toggleCategory({$category.id})">
                            <span class="category-name-text">{$category.name}</span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="{$category.description}">{$category.description ?: '-'}</span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="{$category.sort_order}"
                               min="1"
                               max="999"
                               data-id="{$category.id}"
                               data-original="{$category.sort_order}"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-{$category.status == 1 ? 'enabled' : 'disabled'}">
                        {$category.status == 1 ? '启用' : '禁用'}
                    </span>
                </td>

                <td>
                    {php}
                        echo \app\model\Category::where('parent_id', $category['id'])->count();
                    {/php}
                </td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); addSubCategory({$category.id})" title="添加子分类">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="action-btn edit" onclick="event.stopPropagation(); editCategory({$category.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus({$category.id}, {$category.status})" title="{$category.status == 1 ? '禁用' : '启用'}">
                            <i class="fas fa-{$category.status == 1 ? 'ban' : 'check'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory({$category.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>

            <!-- 二级分类 -->
            {if !empty($category.children)}
            {volist name="category.children" id="child"}
            <tr class="category-row level-2 category-child collapsed"
                data-id="{$child.id}"
                data-level="2"
                data-parent="{$category.id}"
                data-sort="{$child.sort_order}"
                onclick="selectCategory({$child.id})">

                <td>
                    <span class="category-id">{$child.id}</span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow {if empty($child.children)}no-children{/if}"
                             onclick="event.stopPropagation(); toggleCategory({$child.id})">
                            {if !empty($child.children)}
                            <i class="fas fa-chevron-right"></i>
                            {/if}
                        </div>

                        <div class="category-icon level-2">
                            <i class="fas fa-folder"></i>
                        </div>

                        <div class="category-name-clickable" onclick="event.stopPropagation(); toggleCategory({$child.id})">
                            <span class="category-name-text">{$child.name}</span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="{$child.description}">{$child.description ?: '-'}</span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="{$child.sort_order}"
                               min="1"
                               max="999"
                               data-id="{$child.id}"
                               data-original="{$child.sort_order}"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-{$child.status == 1 ? 'enabled' : 'disabled'}">
                        {$child.status == 1 ? '启用' : '禁用'}
                    </span>
                </td>

                <td>
                    {php}
                        echo \app\model\Category::where('parent_id', $child['id'])->count();
                    {/php}
                </td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); addSubCategory({$child.id})" title="添加子分类">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="action-btn edit" onclick="event.stopPropagation(); editCategory({$child.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus({$child.id}, {$child.status})" title="{$child.status == 1 ? '禁用' : '启用'}">
                            <i class="fas fa-{$child.status == 1 ? 'ban' : 'check'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory({$child.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>

            <!-- 三级分类 -->
            {if !empty($child.children)}
            {volist name="child.children" id="grandchild"}
            <tr class="category-row level-3 category-child collapsed"
                data-id="{$grandchild.id}"
                data-level="3"
                data-parent="{$child.id}"
                data-sort="{$grandchild.sort_order}"
                onclick="selectCategory({$grandchild.id})">

                <td>
                    <span class="category-id">{$grandchild.id}</span>
                </td>

                <td>
                    <div class="category-name-cell">
                        <div class="expand-arrow no-children"></div>

                        <div class="category-icon level-3">
                            <i class="fas fa-file"></i>
                        </div>

                        <div class="category-name-clickable">
                            <span class="category-name-text">{$grandchild.name}</span>
                        </div>
                    </div>
                </td>

                <td>
                    <span title="{$grandchild.description}">{$grandchild.description ?: '-'}</span>
                </td>

                <td>
                    <div class="sort-control">
                        <input type="number"
                               class="sort-input"
                               value="{$grandchild.sort_order}"
                               min="1"
                               max="999"
                               data-id="{$grandchild.id}"
                               data-original="{$grandchild.sort_order}"
                               onchange="updateSort(this)"
                               onclick="event.stopPropagation()">
                    </div>
                </td>

                <td>
                    <span class="modern-badge modern-badge-{$grandchild.status == 1 ? 'enabled' : 'disabled'}">
                        {$grandchild.status == 1 ? '启用' : '禁用'}
                    </span>
                </td>

                <td>-</td>

                <td>
                    <div class="category-actions">
                        <button class="action-btn edit" onclick="event.stopPropagation(); editCategory({$grandchild.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle" onclick="event.stopPropagation(); toggleStatus({$grandchild.id}, {$grandchild.status})" title="{$grandchild.status == 1 ? '禁用' : '启用'}">
                            <i class="fas fa-{$grandchild.status == 1 ? 'ban' : 'check'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="event.stopPropagation(); deleteCategory({$grandchild.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            {/volist}
            {/if}
            {/volist}
            {/if}
            {/volist}
        </tbody>
    </table>
        {/empty}
    </div>
</div>
{/block}

{block name="script"}
<script>
    let selectedCategoryId = null;

    // 选择分类
    function selectCategory(id) {
        // 移除之前的选中状态
        document.querySelectorAll('.category-row.selected').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加新的选中状态
        const item = document.querySelector(`[data-id="${id}"]`);
        if (item) {
            item.classList.add('selected');
            selectedCategoryId = id;
        }
    }

    // 切换分类展开/收起（一次只能展开一个）
    function toggleCategory(id) {
        const currentRow = document.querySelector(`[data-id="${id}"]`);
        const arrow = currentRow.querySelector('.expand-arrow');
        const children = document.querySelectorAll(`[data-parent="${id}"]`);

        if (children.length === 0) {
            // 如果没有子分类，只选中当前行
            selectCategory(id);
            return;
        }

        const isExpanded = arrow.classList.contains('expanded');
        const currentLevel = parseInt(currentRow.dataset.level);

        if (isExpanded) {
            // 收起当前分类
            arrow.classList.remove('expanded');
            children.forEach(child => {
                child.style.display = 'none';
                // 同时收起子分类的子分类
                const childId = child.dataset.id;
                const grandChildren = document.querySelectorAll(`[data-parent="${childId}"]`);
                grandChildren.forEach(grandChild => {
                    grandChild.style.display = 'none';
                });
                const childArrow = child.querySelector('.expand-arrow');
                if (childArrow) {
                    childArrow.classList.remove('expanded');
                }
            });
        } else {
            // 先收起同级的其他分类
            const parentId = currentRow.dataset.parent || '0';
            const siblings = document.querySelectorAll(`[data-level="${currentLevel}"][data-parent="${parentId}"]`);

            siblings.forEach(sibling => {
                if (sibling.dataset.id !== id) {
                    const siblingArrow = sibling.querySelector('.expand-arrow');
                    if (siblingArrow && siblingArrow.classList.contains('expanded')) {
                        siblingArrow.classList.remove('expanded');
                        // 收起兄弟分类的子分类
                        const siblingChildren = document.querySelectorAll(`[data-parent="${sibling.dataset.id}"]`);
                        siblingChildren.forEach(child => {
                            child.style.display = 'none';
                            // 同时收起子分类的子分类
                            const childId = child.dataset.id;
                            const grandChildren = document.querySelectorAll(`[data-parent="${childId}"]`);
                            grandChildren.forEach(grandChild => {
                                grandChild.style.display = 'none';
                            });
                            const childArrow = child.querySelector('.expand-arrow');
                            if (childArrow) {
                                childArrow.classList.remove('expanded');
                            }
                        });
                    }
                }
            });

            // 展开当前分类
            arrow.classList.add('expanded');
            children.forEach(child => {
                child.style.display = 'table-row';
            });
        }

        // 选中当前分类
        selectCategory(id);
    }

    // 展开全部
    function expandAll() {
        document.querySelectorAll('.expand-arrow').forEach(arrow => {
            if (!arrow.classList.contains('no-children')) {
                arrow.classList.add('expanded');
            }
        });
        document.querySelectorAll('.category-child').forEach(child => {
            child.style.display = 'table-row';
        });
    }

    // 收起全部
    function collapseAll() {
        document.querySelectorAll('.expand-arrow').forEach(arrow => {
            arrow.classList.remove('expanded');
        });
        document.querySelectorAll('.category-child').forEach(child => {
            child.style.display = 'none';
        });
    }

    // 添加子分类
    function addSubCategory(parentId = null) {
        const id = parentId || selectedCategoryId;
        if (id) {
            window.location.href = `/categories/add?parent_id=${id}`;
        } else {
            window.location.href = '/categories/add';
        }
    }

    // 编辑分类
    function editCategory(id = null) {
        const categoryId = id || selectedCategoryId;
        if (categoryId) {
            window.location.href = `/categories/edit?id=${categoryId}`;
        }
    }

    // 切换状态
    function toggleStatus(id = null, currentStatus = null) {
        const categoryId = id || selectedCategoryId;
        if (!categoryId) return;

        // 如果没有传入当前状态，从DOM中获取
        if (currentStatus === null) {
            const item = document.querySelector(`[data-id="${categoryId}"]`);
            const statusBadge = item.querySelector('.status-badge');
            currentStatus = statusBadge.classList.contains('status-enabled') ? 1 : 0;
        }

        const newStatus = currentStatus === 1 ? 0 : 1;
        const statusText = newStatus === 1 ? '启用' : '禁用';

        if (!confirm(`确定要${statusText}此分类吗？`)) {
            return;
        }

        fetch('/categories/updateStatus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: categoryId, status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }

    // 删除分类
    function deleteCategory(id = null) {
        const categoryId = id || selectedCategoryId;
        if (!categoryId) return;

        if (!confirm('确定要删除此分类吗？删除后不可恢复！')) {
            return;
        }

        fetch('/categories/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: categoryId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }

    // 导入分类
    function importCategories() {
        // 显示导入选项对话框
        const choice = confirm(`分类数据导入\n\n支持格式：JSON、CSV\n\n点击"确定"选择文件导入\n点击"取消"下载导入模板\n\n模板说明：\n- name: 分类名称（必填）\n- description: 分类描述\n- parent_id: 父分类ID（0为顶级）\n- sort_order: 排序权重\n- status: 状态（1启用，0禁用）`);

        if (!choice) {
            // 用户选择下载模板
            downloadTemplate();
            return;
        }

        // 创建文件输入元素
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json,.csv';
        input.style.display = 'none';

        input.onchange = function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            // 显示上传进度
            if (confirm(`确定要导入文件"${file.name}"吗？\n\n注意：导入过程中请勿关闭页面`)) {
                // 显示加载提示
                const originalText = document.querySelector('.header-btn.btn-success span').textContent;
                document.querySelector('.header-btn.btn-success span').textContent = '导入中...';
                document.querySelector('.header-btn.btn-success').disabled = true;

                fetch('/categories/import', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert(`导入成功！\n\n${data.message}`);
                        location.reload();
                    } else {
                        alert(`导入失败：${data.message}`);
                    }
                })
                .catch(error => {
                    alert('导入失败，请检查文件格式是否正确');
                })
                .finally(() => {
                    // 恢复按钮状态
                    document.querySelector('.header-btn.btn-success span').textContent = originalText;
                    document.querySelector('.header-btn.btn-success').disabled = false;
                });
            }
        };

        document.body.appendChild(input);
        input.click();
        document.body.removeChild(input);
    }

    // 下载导入模板
    function downloadTemplate() {
        const format = prompt('请选择模板格式：\n\n1. JSON格式 (输入: json)\n2. CSV格式 (输入: csv)\n\n请输入格式代码：', 'json');

        if (!format || !['json', 'csv'].includes(format)) {
            if (format !== null) {
                alert('请输入有效的格式代码：json 或 csv');
            }
            return;
        }

        const link = document.createElement('a');
        link.href = `/templates/category_import_template.${format}`;
        link.download = `category_import_template.${format}`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            alert(`模板文件已下载，请参考模板格式准备导入数据`);
        }, 100);
    }

    // 导出分类
    function exportCategories() {
        const format = prompt(`分类数据导出\n\n请选择导出格式：\n\n1. JSON格式 - 适合程序处理 (输入: json)\n2. CSV格式 - 适合Excel打开 (输入: csv)\n\n请输入格式代码：`, 'csv');

        if (!format || !['json', 'csv'].includes(format)) {
            if (format !== null) {
                alert('请输入有效的格式代码：json 或 csv');
            }
            return;
        }

        // 显示导出进度
        const originalText = document.querySelector('.header-btn.btn-warning span').textContent;
        document.querySelector('.header-btn.btn-warning span').textContent = '导出中...';
        document.querySelector('.header-btn.btn-warning').disabled = true;

        // 创建下载链接
        const link = document.createElement('a');
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
        link.href = `/categories/export?format=${format}`;
        link.download = `categories_${timestamp}.${format}`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 恢复按钮状态
        setTimeout(() => {
            document.querySelector('.header-btn.btn-warning span').textContent = originalText;
            document.querySelector('.header-btn.btn-warning').disabled = false;

            const formatName = format === 'json' ? 'JSON' : 'CSV';
            alert(`${formatName}格式的分类数据已开始下载\n\n文件名：categories_${timestamp}.${format}`);
        }, 500);
    }

    // 排序更新功能

    // 更新排序
    function updateSort(input) {
        const categoryId = input.dataset.id;
        const newSort = parseInt(input.value);
        const originalSort = parseInt(input.dataset.original);

        // 如果值没有改变，直接返回
        if (newSort === originalSort) {
            input.classList.remove('changed');
            return;
        }

        // 验证输入值
        if (isNaN(newSort) || newSort < 1 || newSort > 999) {
            showToast('排序值必须在1-999之间', 'error');
            input.value = originalSort;
            input.classList.remove('changed');
            return;
        }

        // 标记为已修改
        input.classList.add('changed');

        // 显示加载提示
        const loadingToast = showToast('正在更新排序...', 'info');

        // 发送更新请求
        console.log('发送排序更新请求:', { id: categoryId, sort_order: newSort });
        fetch('/categories/updateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: categoryId,
                sort_order: newSort
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('排序更新响应:', data);
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast('排序更新成功！', 'success');

                // 更新原始值
                input.dataset.original = newSort;
                input.classList.remove('changed');

                // 更新行的data-sort属性
                const row = input.closest('.category-row');
                if (row) {
                    row.dataset.sort = newSort;
                }

                // 添加成功动画
                input.style.animation = 'flash-success 0.6s ease';
                setTimeout(() => {
                    input.style.animation = '';
                    // 延迟刷新页面以显示新的排序
                    showToast('正在刷新页面以显示新排序...', 'info');
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                }, 800);

            } else {
                showToast('排序更新失败：' + data.message, 'error');
                input.value = originalSort;
                input.classList.remove('changed');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('排序更新失败，请稍后重试', 'error');
            console.error('排序更新错误:', error);
            input.value = originalSort;
            input.classList.remove('changed');
        });
    }



    // 更新分类排序
    function updateCategorySort(categoryId, newSort) {
        // 显示加载提示
        const loadingToast = showToast('正在更新排序...', 'info');

        fetch('/categories/updateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: categoryId,
                sort_order: newSort
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('排序更新成功', 'success');
                // 延迟刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast('排序更新失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('排序更新失败，请稍后重试', 'error');
        });
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 自动隐藏
        if (type !== 'info') {
            setTimeout(() => {
                hideToast(toast);
            }, 3000);
        }

        return toast;
    }

    // 隐藏提示消息
    function hideToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }







    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 默认收起所有节点
        collapseAll();

        // 如果有分类，默认选中第一个
        const firstCategory = document.querySelector('.category-row');
        if (firstCategory) {
            const firstId = firstCategory.dataset.id;
            selectCategory(firstId);
        }




    });

    // 显示层级说明弹窗
    function showHierarchyHelp() {
        const modal = document.getElementById('hierarchyModal');
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // 关闭层级说明弹窗
    function closeHierarchyHelp() {
        const modal = document.getElementById('hierarchyModal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const modal = document.getElementById('hierarchyModal');
        if (event.target === modal) {
            closeHierarchyHelp();
        }
    }
</script>

<!-- 层级说明模态框 -->
<div id="hierarchyModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-sitemap"></i> 分类层级结构说明</h2>
            <span class="close" onclick="closeHierarchyHelp()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="help-section">
                <h3><i class="fas fa-layer-group"></i> 分类层级说明</h3>
                <div class="hierarchy-levels">
                    <div class="level-item">
                        <span class="level-badge level-1">一级</span>
                        <div class="level-content">
                            <span class="level-desc">主分类 - 最多支持3级分类结构</span>
                            <span class="level-example">如：会员卡密、软件授权、数字内容</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-2">二级</span>
                        <div class="level-content">
                            <span class="level-desc">子分类 - 归属于一级分类</span>
                            <span class="level-example">如：基础会员、高级会员、开发工具</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-3">三级</span>
                        <div class="level-content">
                            <span class="level-desc">子子分类 - 归属于二级分类</span>
                            <span class="level-example">如：月度会员、年度会员、专业版</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="help-section">
                <h3><i class="fas fa-sort-numeric-down"></i> 排序功能说明</h3>
                <div class="sort-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-arrow-up text-primary"></i>
                            <div>
                                <strong>排序规则</strong>
                                <p>数字越小，排序越靠前</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-layer-group text-success"></i>
                            <div>
                                <strong>同级排序</strong>
                                <p>同级分类之间可以调整排序</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-save text-warning"></i>
                            <div>
                                <strong>自动保存</strong>
                                <p>修改后自动保存并刷新显示</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-hashtag text-info"></i>
                            <div>
                                <strong>数值范围</strong>
                                <p>排序值范围：1-999</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" onclick="closeHierarchyHelp()">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>
</div>

{/block}
