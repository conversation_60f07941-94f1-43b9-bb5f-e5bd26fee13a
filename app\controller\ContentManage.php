<?php

namespace app\controller;

use app\BaseController;
use app\model\Content;
use app\model\Category;
use app\model\Card;

class ContentManage extends BaseController
{
    /**
     * 内容管理首页
     */
    public function index()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $status = $this->request->param('status', '');
        $keyword = $this->request->param('keyword', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['c.category_id', '=', $category_id];
        }
        if ($status !== '') {
            $where[] = ['c.status', '=', $status];
        }
        if ($keyword) {
            $where[] = ['c.title|c.content', 'like', '%' . $keyword . '%'];
        }

        // 查询内容列表
        $contents = Content::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.id',
                'c.title',
                'c.content',
                'c.category_id',
                'c.sort_order',
                'c.status',
                'c.created_at',
                'c.updated_at',
                'cat.name as category_name'
            ])
            ->where($where)
            ->order('c.sort_order', 'desc')
            ->order('c.created_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        // 获取统计数据
        $stats = Content::getStats();

        return view('content-manage/index', [
            'contents' => $contents,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'category_id' => $category_id,
                'keyword' => $keyword
            ]
        ]);
    }

    /**
     * 创建内容页面
     */
    public function create()
    {
        if ($this->request->isPost()) {
            return $this->save();
        }

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        return view('content-manage/create', [
            'categories' => $categories
        ]);
    }

    /**
     * 编辑内容页面
     */
    public function edit()
    {
        $id = $this->request->param('id');
        
        if ($this->request->isPost()) {
            return $this->save();
        }

        $content = Content::find($id);
        if (!$content) {
            $this->error('内容不存在');
        }

        // 获取分类列表
        $categories = Category::where('status', Category::STATUS_ENABLED)
            ->order('sort_order', 'desc')
            ->select();

        return view('content-manage/edit', [
            'content' => $content,
            'categories' => $categories
        ]);
    }

    /**
     * 保存内容
     */
    public function save()
    {
        $data = $this->request->post();

        // 验证数据
        $validate = $this->validate($data, [
            'title' => 'require|max:255',
            'content' => 'require',
            'category_id' => 'require|integer',
            'sort_order' => 'integer|between:0,9999'
        ]);

        if ($validate !== true) {
            return json(['code' => 400, 'message' => $validate]);
        }

        try {
            if (isset($data['id']) && $data['id']) {
                // 更新
                $content = Content::find($data['id']);
                if (!$content) {
                    return json(['code' => 404, 'message' => '内容不存在']);
                }
            } else {
                // 新增
                $content = new Content();
            }

            $content->title = $data['title'];
            $content->content = $data['content'];
            $content->category_id = $data['category_id'];
            $content->sort_order = $data['sort_order'] ?? 0;
            $content->status = $data['status'] ?? Content::STATUS_ENABLED;
            $content->save();

            return json(['code' => 200, 'message' => '保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除内容
     */
    public function delete()
    {
        $id = $this->request->param('id');
        
        try {
            $content = Content::find($id);
            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            // 检查是否有关联的卡密
            $cardCount = Card::where('content_id', $id)->count();
            if ($cardCount > 0) {
                return json(['code' => 400, 'message' => "该内容已关联 {$cardCount} 个卡密，无法删除"]);
            }

            $content->delete();
            return json(['code' => 200, 'message' => '删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新排序
     */
    public function updateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();
        
        if (empty($data['id']) || !isset($data['sort_order'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        $sortOrder = intval($data['sort_order']);
        if ($sortOrder < 0 || $sortOrder > 9999) {
            return json(['code' => 400, 'message' => '排序值必须在0-9999之间']);
        }

        try {
            $content = Content::find($data['id']);
            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            $content->sort_order = $sortOrder;
            $content->save();

            return json(['code' => 200, 'message' => '排序更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '排序更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量更新排序
     */
    public function batchUpdateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();
        
        if (empty($data['ids']) || !isset($data['start_sort'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        $startSort = intval($data['start_sort']);
        if ($startSort < 0 || $startSort > 9999) {
            return json(['code' => 400, 'message' => '起始排序值必须在0-9999之间']);
        }

        try {
            $ids = explode(',', $data['ids']);
            $currentSort = $startSort;
            
            foreach ($ids as $id) {
                Content::where('id', $id)->update(['sort_order' => $currentSort]);
                $currentSort++;
            }

            $count = count($ids);
            return json(['code' => 200, 'message' => "成功更新 {$count} 个内容的排序"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '批量排序更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换状态
     */
    public function toggleStatus()
    {
        $id = $this->request->param('id');
        
        try {
            $content = Content::find($id);
            if (!$content) {
                return json(['code' => 404, 'message' => '内容不存在']);
            }

            $content->status = $content->status == Content::STATUS_ENABLED ? Content::STATUS_DISABLED : Content::STATUS_ENABLED;
            $content->save();

            $statusText = $content->status == Content::STATUS_ENABLED ? '启用' : '禁用';
            return json(['code' => 200, 'message' => "内容已{$statusText}"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '状态更新失败：' . $e->getMessage()]);
        }
    }
}
