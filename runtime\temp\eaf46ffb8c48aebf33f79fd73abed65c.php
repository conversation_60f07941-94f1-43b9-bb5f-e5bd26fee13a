<?php /*a:2:{s:44:"F:\linshi\thphp\kmxt\view\content\index.html";i:1754021713;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754022661;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容管理</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --success-light: #6ee7b7;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-active: var(--primary-color);
            --content-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            overflow-y: auto;
            will-change: transform;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 0;
            will-change: margin-left;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 内容容器 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }

        /* 响应式内边距 */
        @media (max-width: 1200px) {
            .content-wrapper {
                padding: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .content-wrapper {
                padding: 0.75rem;
            }
        }

        /* 现代化卡片样式 */
        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .modern-card-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modern-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid var(--border-color);
        }

        /* 现代化按钮样式 */
        .modern-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius-lg);
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, var(--success-color) 100%);
            box-shadow: 0 6px 20px 0 rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .modern-btn-outline {
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .modern-btn-outline:hover {
            background: white;
            color: var(--text-primary);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* 现代化表单样式 */
        .modern-form-group {
            margin-bottom: 1.5rem;
        }

        .modern-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .modern-form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: white;
        }

        .modern-form-control::placeholder {
            color: var(--text-muted);
        }

        /* 现代化表格样式 */
        .modern-table-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            font-size: 0.875rem;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 现代化状态标签 */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .modern-badge-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .modern-badge-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .modern-badge-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .modern-badge-primary {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        /* 现代化统计卡片 */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .modern-stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modern-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .modern-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .modern-stats-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem auto;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        .modern-stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .modern-stats-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .modern-stats-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .modern-stats-trend.positive {
            color: var(--success-color);
        }

        .modern-stats-trend.negative {
            color: var(--error-color);
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content-manage" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 32px;
    }

    .page-title-main {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .header-btn.btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .header-btn.btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .header-btn.btn-success:hover {
        background: #218838;
        border-color: #218838;
        color: white;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
    }

    .header-btn.btn-warning {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .header-btn.btn-warning:hover {
        background: #5a6268;
        border-color: #5a6268;
        color: white;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25);
    }

    .header-btn i {
        font-size: 11px;
    }

    /* 统计卡片 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 110px;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-color);
        border-radius: 12px 12px 0 0;
    }

    .stat-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .stat-icon.blue {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: white;
    }

    .stat-icon.green {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        color: white;
    }

    .stat-icon.orange {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
        color: white;
    }

    .stat-icon.red {
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
        color: white;
    }

    .stat-label {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #262626;
        line-height: 1;
        margin: 0;
    }

    /* 筛选工具栏 */
    .toolbar {
        background: white;
        padding: 20px 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        margin-bottom: 24px;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .btn i {
        font-size: 11px;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    /* 筛选表单 */
    .filter-form {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: nowrap;
    }

    .form-select, .form-input {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 13px;
        transition: all 0.2s ease;
    }

    .form-select:focus, .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-select {
        min-width: 140px;
        background: white;
        cursor: pointer;
    }

    .form-input {
        min-width: 220px;
        flex: 1;
    }

    /* 分类层级样式 */
    .form-select option {
        padding: 6px 8px;
        line-height: 1.5;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .category-option-level-1 {
        font-weight: 700;
        color: #1890ff !important;
        background-color: #f0f9ff !important;
    }

    .category-option-level-2 {
        color: #52c41a !important;
        font-weight: 600;
        background-color: #f6ffed !important;
    }

    .category-option-level-3 {
        color: #fa8c16 !important;
        font-weight: 500;
        background-color: #fff7e6 !important;
        font-size: 13px;
    }

    /* 选中状态样式 */
    .form-select option:checked {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    /* 增强下拉框样式 */
    .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 8px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 32px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-select:hover {
        border-color: #40a9ff;
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
    }

    /* 分类选择框特殊样式 */
    select[name="category_id"] {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }



    /* 筛选标签样式 */
    .filter-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
        margin-right: 4px;
        white-space: nowrap;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* 内容表格 */
    .content-table {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        overflow: hidden;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .table th {
        background: #fafafa;
        padding: 16px 12px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        font-size: 13px;
    }

    .table td {
        padding: 16px 12px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    /* 状态标签 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
    }

    .status-unused {
        background: #e6f7ff;
        color: #1890ff;
    }

    .status-used {
        background: #f6ffed;
        color: #52c41a;
    }

    .status-disabled {
        background: #fff2e8;
        color: #fa8c16;
    }

    /* 内容预览 */
    .content-preview {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .content-preview:hover {
        color: var(--primary-color);
    }

    /* 操作按钮 */
    .action-btn {
        padding: 4px 8px;
        font-size: 11px;
        border-radius: 4px;
        margin-right: 4px;
    }

    /* 分页 */
    .pagination-wrapper {
        padding: 20px 24px;
        background: white;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* 响应式 */
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column !important;
            align-items: stretch !important;
            gap: 16px;
        }

        .header-actions {
            justify-content: center;
            flex-wrap: wrap;
        }

        .filter-form {
            flex-wrap: wrap;
            gap: 8px;
        }

        .form-select {
            min-width: 120px;
            flex: 1;
        }

        .form-input {
            min-width: 180px;
            flex: 2;
        }

        .btn {
            flex-shrink: 0;
            white-space: nowrap;
        }
    }

    @media (max-width: 480px) {
        .filter-form {
            flex-direction: column;
            align-items: stretch;
        }

        .form-select,
        .form-input,
        .btn {
            width: 100%;
            min-width: auto;
        }
    }

    /* 排序控件样式 */
    .sort-controls {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .sort-controls .btn {
        padding: 2px 6px;
        font-size: 10px;
        line-height: 1;
        border-radius: 3px;
    }

    .sort-input {
        text-align: center;
        font-size: 12px;
    }

    .sort-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    }

    .content-preview {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .content-preview:hover {
        color: var(--primary-color);
        background-color: rgba(99, 102, 241, 0.05);
        border-radius: 4px;
        padding: 2px 4px;
    }

    /* 内容标题单元格样式 */
    .content-title-cell {
        min-width: 200px;
    }

    .content-title {
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 4px;
        border-radius: 6px;
    }

    .content-title:hover {
        background-color: rgba(99, 102, 241, 0.05);
        transform: translateY(-1px);
    }

    .content-title .fw-bold {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 4px;
    }

    .content-title .small {
        font-size: 11px;
        opacity: 0.8;
    }

    .sortable-row {
        transition: all 0.3s ease;
    }

    .sortable-row:hover {
        background-color: rgba(99, 102, 241, 0.02);
    }

    /* 内容管理页面专用样式 */
    .modern-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    @media (max-width: 1200px) {
        .modern-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .sort-controls {
            flex-direction: row;
        }
    }

    @media (max-width: 768px) {
        .sort-input {
            width: 60px !important;
        }
    }
</style>

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">内容管理</h1>
        <p class="text-muted mb-0">管理卡密的兑换内容，支持批量编辑、排序和导出功能</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="batchEditContent()">
            <i class="fas fa-edit"></i>
            批量编辑
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="clearSelectedContent()">
            <i class="fas fa-eraser"></i>
            清空内容
        </button>
        <a href="/content/export<?php if($_GET): ?>?<?php echo htmlentities((string) $_SERVER['QUERY_STRING']); ?><?php endif; ?>" class="modern-btn modern-btn-primary btn-sm">
            <i class="fas fa-download"></i>
            导出
        </a>
    </div>
</div>

<!-- 统计概况 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-file-alt"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['total']); ?></div>
        <div class="modern-stats-label">总卡密数</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['has_content']); ?></div>
        <div class="modern-stats-label">有内容</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['no_content']); ?></div>
        <div class="modern-stats-label">无内容</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="modern-stats-value"><?php echo htmlentities((string) $stats['expired']); ?></div>
        <div class="modern-stats-label">已过期</div>
    </div>
</div>

<!-- 筛选工具栏 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h5>
        <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="resetFilters()">
            <i class="fas fa-undo"></i>
            重置
        </button>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">分类</label>
                        <select name="category_id" class="modern-form-control">
                            <option value="">全部分类</option>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $category['id']); ?>"
                                    class="category-option-level-<?php echo htmlentities((string) $category['level']); ?>"
                                    <?php if($filters['category_id'] == $category['id']): ?>selected<?php endif; ?>>
                                <?php switch($category['level']): case "1": ?>
                                        ■ <?php echo htmlentities((string) $category['name']); break; case "2": ?>
                                        &nbsp;&nbsp;&nbsp;&nbsp;▶ <?php echo htmlentities((string) $category['name']); break; case "3": ?>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;● <?php echo htmlentities((string) $category['name']); break; ?>
                                <?php endswitch; ?>
                            </option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">状态</label>
                        <select name="status" class="modern-form-control">
                            <option value="">全部状态</option>
                            <option value="0" <?php if($filters['status'] === '0'): ?>selected<?php endif; ?>>未使用</option>
                            <option value="1" <?php if($filters['status'] === '1'): ?>selected<?php endif; ?>>已使用</option>
                            <option value="2" <?php if($filters['status'] === '2'): ?>selected<?php endif; ?>>已禁用</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">内容状态</label>
                        <select name="has_content" class="modern-form-control">
                            <option value="">全部</option>
                            <option value="1" <?php if($filters['has_content'] === '1'): ?>selected<?php endif; ?>>有内容</option>
                            <option value="0" <?php if($filters['has_content'] === '0'): ?>selected<?php endif; ?>>无内容</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">搜索关键词</label>
                        <input type="text" name="keyword" class="modern-form-control" placeholder="卡密编号或内容..."
                               value="<?php echo htmlentities((string) $filters['keyword']); ?>">
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">&nbsp;</label>
                        <button type="submit" class="modern-btn modern-btn-primary w-100">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 内容列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            内容列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="selectAll()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="batchUpdateSort()">
                <i class="fas fa-sort"></i>
                批量排序
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>内容标题</th>
                        <th>分类</th>
                        <th>状态</th>
                        <th>排序</th>
                        <th>创建时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="contentTableBody">
                    <?php if(is_array($contents) || $contents instanceof \think\Collection || $contents instanceof \think\Paginator): $i = 0; $__LIST__ = $contents;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$content): $mod = ($i % 2 );++$i;?>
                    <tr data-id="<?php echo htmlentities((string) $content['id']); ?>" class="sortable-row">
                        <td>
                            <input type="checkbox" class="content-checkbox" value="<?php echo htmlentities((string) $content['id']); ?>">
                        </td>
                        <td>
                            <div class="content-title-cell">
                                <div class="content-title" onclick="showContentModal('<?php echo htmlentities((string) $content['id']); ?>', '<?php echo htmlentities((string) (isset($content['title']) && ($content['title'] !== '')?$content['title']:$content['card_code'])); ?>', `<?php echo $content['content']; ?>`)"
                                     title="点击查看完整内容">
                                    <?php if($content['content']): ?>
                                        <div class="fw-bold text-dark mb-1">
                                            <?php 
                                                $title = $content['title'] ?: mb_substr(strip_tags($content['content']), 0, 30, 'utf-8') ?: '无标题';
                                                echo htmlentities($title);
                                                if (!$content['title'] && mb_strlen($content['content'], 'utf-8') > 30) {
                                                    echo '...';
                                                }
                                             ?>
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-credit-card me-1"></i>
                                            <?php echo htmlentities((string) $content['card_code']); ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted fst-italic">
                                            <i class="fas fa-file-alt me-1"></i>
                                            暂无内容
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-credit-card me-1"></i>
                                            <?php echo htmlentities((string) $content['card_code']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="modern-badge modern-badge-primary"><?php echo htmlentities((string) $content['category_name']); ?></span>
                        </td>
                        <td>
                            <?php switch($content['status']): case "0": ?>
                                    <span class="modern-badge modern-badge-warning">
                                        <i class="fas fa-clock"></i>
                                        未使用
                                    </span>
                                <?php break; case "1": ?>
                                    <span class="modern-badge modern-badge-success">
                                        <i class="fas fa-check-circle"></i>
                                        已使用
                                    </span>
                                <?php break; case "2": ?>
                                    <span class="modern-badge modern-badge-error">
                                        <i class="fas fa-ban"></i>
                                        已禁用
                                    </span>
                                <?php break; ?>
                            <?php endswitch; ?>
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <input type="number" class="form-control form-control-sm sort-input"
                                       value="<?php echo htmlentities((string) (isset($content['sort_order']) && ($content['sort_order'] !== '')?$content['sort_order']:0)); ?>"
                                       min="0" max="9999"
                                       style="width: 70px;"
                                       onchange="updateSort(<?php echo htmlentities((string) $content['id']); ?>, this.value)"
                                       data-original="<?php echo htmlentities((string) (isset($content['sort_order']) && ($content['sort_order'] !== '')?$content['sort_order']:0)); ?>">
                                <div class="sort-controls">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveUp(<?php echo htmlentities((string) $content['id']); ?>)" title="上移">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveDown(<?php echo htmlentities((string) $content['id']); ?>)" title="下移">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td class="text-muted">
                            <small><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($content['created_at'])? strtotime($content['created_at']) : $content['created_at'])); ?></small>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="/content/edit?id=<?php echo htmlentities((string) $content['id']); ?>" class="modern-btn modern-btn-outline btn-sm" title="编辑内容">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="duplicateContent(<?php echo htmlentities((string) $content['id']); ?>)" title="复制内容">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="deleteContent(<?php echo htmlentities((string) $content['id']); ?>)" title="删除内容">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; if(empty($contents) || (($contents instanceof \think\Collection || $contents instanceof \think\Paginator ) && $contents->isEmpty())): ?>
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div class="h5">暂无内容数据</div>
                                <p>当前筛选条件下没有找到相关内容</p>
                                <button class="modern-btn modern-btn-primary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i>
                                    重置筛选
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 分页 -->
    <?php if($contents->hasPages()): ?>
    <div class="modern-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                显示第 <?php echo htmlentities((string) $contents->currentPage()); ?> 页，共 <?php echo htmlentities((string) $contents->lastPage()); ?> 页，总计 <?php echo htmlentities((string) $contents->total()); ?> 条记录
            </div>
            <div>
                <?php echo $contents->render(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 内容详情模态框 -->
<div id="contentModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3 id="modalTitle">内容详情</h3>
            <span class="close" onclick="closeContentModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>内容：</label>
                <textarea id="modalContent" rows="8" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" readonly></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeContentModal()">关闭</button>
            <button class="modern-btn modern-btn-primary" onclick="editFromModal()">编辑</button>
        </div>
    </div>
</div>

<!-- 批量编辑模态框 -->
<div id="batchEditModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3>批量编辑内容</h3>
            <span class="close" onclick="closeBatchEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="batchEditForm">
                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">新内容：</label>
                    <textarea name="content" rows="6" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"
                              placeholder="请输入要批量设置的内容..." required></textarea>
                </div>
                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">过期时间（可选）：</label>
                    <input type="datetime-local" name="expire_at" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div class="selected-info" style="background: #f8f9fa; padding: 12px; border-radius: 4px; margin-bottom: 16px;">
                    已选择 <span id="selectedCount">0</span> 个卡密
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeBatchEditModal()">取消</button>
            <button class="modern-btn modern-btn-primary" onclick="submitBatchEdit()">确认更新</button>
        </div>
    </div>
</div>

<script>
    let currentContentId = null;

    // 全选/取消全选
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.content-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateSelectedCount();
    }

    // 更新选中数量
    function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        const count = checkboxes.length;

        const selectedCountElement = document.getElementById('selectedCount');
        if (selectedCountElement) {
            selectedCountElement.textContent = count;
        }

        return count;
    }

    // 监听复选框变化
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('content-checkbox')) {
            updateSelectedCount();

            // 更新全选状态
            const checkboxes = document.querySelectorAll('.content-checkbox');
            const checkedBoxes = document.querySelectorAll('.content-checkbox:checked');
            const selectAll = document.getElementById('selectAll');

            if (checkedBoxes.length === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (checkedBoxes.length === checkboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }
        }
    });

    // 显示内容详情模态框
    function showContentModal(id, cardCode, content) {
        currentContentId = id;
        document.getElementById('modalTitle').textContent = `内容详情 - ${cardCode}`;
        document.getElementById('modalContent').value = content || '';
        document.getElementById('contentModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // 关闭内容详情模态框
    function closeContentModal() {
        document.getElementById('contentModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        currentContentId = null;
    }

    // 从模态框编辑
    function editFromModal() {
        if (currentContentId) {
            window.location.href = `/content/edit?id=${currentContentId}`;
        }
    }

    // 批量编辑内容
    function batchEditContent() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要编辑的卡密', 'warning');
            return;
        }

        updateSelectedCount();
        document.getElementById('batchEditModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // 关闭批量编辑模态框
    function closeBatchEditModal() {
        document.getElementById('batchEditModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        document.getElementById('batchEditForm').reset();
    }

    // 提交批量编辑
    function submitBatchEdit() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要编辑的卡密', 'warning');
            return;
        }

        const form = document.getElementById('batchEditForm');
        const formData = new FormData(form);

        if (!formData.get('content').trim()) {
            showToast('请输入内容', 'warning');
            return;
        }

        const loadingToast = showToast('正在批量更新...', 'info');

        fetch('/content/batchEdit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(','),
                content: formData.get('content'),
                expire_at: formData.get('expire_at')
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast(data.message, 'success');
                closeBatchEditModal();
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('批量更新失败，请稍后重试', 'error');
            console.error('Error:', error);
        });
    }

    // 清空选中内容
    function clearSelectedContent() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要清空的卡密', 'warning');
            return;
        }

        if (!confirm(`确定要清空 ${selectedIds.length} 个卡密的内容吗？此操作不可撤销。`)) {
            return;
        }

        const loadingToast = showToast('正在清空内容...', 'info');

        fetch('/content/clearContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(',')
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('清空失败，请稍后重试', 'error');
            console.error('Error:', error);
        });
    }

    // 获取选中的ID列表
    function getSelectedIds() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    // 重置筛选条件
    function resetFilters() {
        const form = document.querySelector('.filter-form');
        const categorySelect = form.querySelector('select[name="category_id"]');
        const statusSelect = form.querySelector('select[name="status"]');
        const keywordInput = form.querySelector('input[name="keyword"]');

        // 检查是否有筛选条件
        const hasFilters = categorySelect.value || statusSelect.value || keywordInput.value.trim();

        if (!hasFilters) {
            showToast('当前没有筛选条件需要重置', 'info');
            return;
        }

        // 重置表单
        categorySelect.value = '';
        statusSelect.value = '';
        keywordInput.value = '';

        // 显示重置提示
        showToast('筛选条件已重置', 'success');

        // 提交表单以应用重置
        setTimeout(() => {
            form.submit();
        }, 500);
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);

        return toast;
    }

    function hideToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 排序功能
    function updateSort(id, sortOrder) {
        fetch('/content/updateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                id: id,
                sort_order: sortOrder
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast('排序更新成功', 'success');
            } else {
                showToast(data.message || '排序更新失败', 'error');
                // 恢复原值
                const input = document.querySelector(`input[onchange*="${id}"]`);
                if (input) {
                    input.value = input.dataset.original;
                }
            }
        })
        .catch(error => {
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 上移
    function moveUp(id) {
        const input = document.querySelector(`input[onchange*="${id}"]`);
        if (input) {
            const currentValue = parseInt(input.value) || 0;
            const newValue = currentValue + 1;
            input.value = newValue;
            updateSort(id, newValue);
        }
    }

    // 下移
    function moveDown(id) {
        const input = document.querySelector(`input[onchange*="${id}"]`);
        if (input) {
            const currentValue = parseInt(input.value) || 0;
            const newValue = Math.max(0, currentValue - 1);
            input.value = newValue;
            updateSort(id, newValue);
        }
    }

    // 批量更新排序
    function batchUpdateSort() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要排序的内容', 'warning');
            return;
        }

        const startSort = prompt('请输入起始排序号（数字越大排序越靠前）:', '100');
        if (startSort === null) return;

        const startSortNum = parseInt(startSort);
        if (isNaN(startSortNum) || startSortNum < 0) {
            showToast('请输入有效的排序号', 'error');
            return;
        }

        const loadingToast = showToast('正在批量更新排序...', 'info');

        fetch('/content/batchUpdateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(','),
                start_sort: startSortNum
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('批量排序更新成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '批量排序更新失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 复制内容
    function duplicateContent(id) {
        if (!confirm('确定要复制这个内容吗？')) {
            return;
        }

        const loadingToast = showToast('正在复制内容...', 'info');

        fetch('/content/duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                id: id
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('内容复制成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '内容复制失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 删除内容
    function deleteContent(id) {
        if (!confirm('确定要删除这个内容吗？此操作将清空该卡密的内容。')) {
            return;
        }

        const loadingToast = showToast('正在删除内容...', 'info');

        fetch('/content/clearContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: id
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('内容删除成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '内容删除失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 全选功能
    function selectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        selectAllCheckbox.checked = true;
        toggleSelectAll();
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const contentModal = document.getElementById('contentModal');
        const batchEditModal = document.getElementById('batchEditModal');

        if (event.target === contentModal) {
            closeContentModal();
        }
        if (event.target === batchEditModal) {
            closeBatchEditModal();
        }
    }
</script>

<!-- 模态框和动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .modal {
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 24px;
        max-height: 50vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 16px 24px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .selected-info {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 16px;
        font-size: 14px;
        color: #666;
    }
</style>


        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
</body>
</html>
