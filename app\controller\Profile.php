<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Session;

/**
 * 个人资料控制器
 */
class Profile extends BaseController
{
    /**
     * 个人资料页面
     */
    public function index()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return redirect('/login');
        }
        
        // 获取管理员信息
        $admin = Db::name('admins')->where('id', $adminId)->find();
        
        if (!$admin) {
            return redirect('/login');
        }
        
        // 获取统计信息
        $stats = $this->getAdminStats($adminId);
        
        return view('profile/index', [
            'admin' => $admin,
            'stats' => $stats
        ]);
    }
    
    /**
     * 更新个人资料
     */
    public function update()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        $data = $this->request->post();
        
        try {
            // 验证数据
            $validate = $this->validate($data, [
                'name' => 'require|max:50',
                'email' => 'email|max:100'
            ]);
            
            if ($validate !== true) {
                return json(['code' => 400, 'message' => $validate]);
            }
            
            // 检查邮箱是否已被其他用户使用
            if (!empty($data['email'])) {
                $existingAdmin = Db::name('admins')
                    ->where('email', $data['email'])
                    ->where('id', '<>', $adminId)
                    ->find();
                    
                if ($existingAdmin) {
                    return json(['code' => 400, 'message' => '该邮箱已被其他用户使用']);
                }
            }
            
            // 更新数据
            $updateData = [
                'name' => $data['name'],
                'email' => $data['email'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $result = Db::name('admins')->where('id', $adminId)->update($updateData);
            
            if ($result !== false) {
                // 更新会话中的用户名
                Session::set('admin_name', $data['name']);
                
                return json(['code' => 200, 'message' => '个人资料更新成功']);
            } else {
                return json(['code' => 500, 'message' => '更新失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        $data = $this->request->post();
        
        try {
            // 验证数据
            $validate = $this->validate($data, [
                'current_password' => 'require',
                'new_password' => 'require|min:6|max:20',
                'confirm_password' => 'require|confirm:new_password'
            ]);
            
            if ($validate !== true) {
                return json(['code' => 400, 'message' => $validate]);
            }
            
            // 获取当前管理员信息
            $admin = Db::name('admins')->where('id', $adminId)->find();
            
            if (!$admin) {
                return json(['code' => 404, 'message' => '用户不存在']);
            }
            
            // 验证当前密码
            if (!password_verify($data['current_password'], $admin['password'])) {
                return json(['code' => 400, 'message' => '当前密码错误']);
            }
            
            // 更新密码
            $newPasswordHash = password_hash($data['new_password'], PASSWORD_DEFAULT);
            
            $result = Db::name('admins')->where('id', $adminId)->update([
                'password' => $newPasswordHash,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result !== false) {
                return json(['code' => 200, 'message' => '密码修改成功']);
            } else {
                return json(['code' => 500, 'message' => '密码修改失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '密码修改失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取管理员统计信息
     */
    private function getAdminStats($adminId)
    {
        try {
            // 获取最后登录时间
            $admin = Db::name('admins')->where('id', $adminId)->find();
            
            return [
                'last_login_time' => $admin['last_login_time'] ?? '从未登录',
                'last_login_ip' => $admin['last_login_ip'] ?? '未知',
                'created_at' => $admin['created_at'] ?? '未知',
                'status' => $admin['status'] == 1 ? '正常' : '禁用'
            ];
            
        } catch (\Exception $e) {
            return [
                'last_login_time' => '获取失败',
                'last_login_ip' => '获取失败',
                'created_at' => '获取失败',
                'status' => '获取失败'
            ];
        }
    }
}
