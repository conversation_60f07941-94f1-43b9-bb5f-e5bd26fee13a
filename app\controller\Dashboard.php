<?php

namespace app\controller;

use app\BaseController;
use app\model\Card;
use app\model\Category;
use app\model\UsageRecord;
use app\model\SystemSetting;

/**
 * 控制台控制器
 */
class Dashboard extends BaseController
{
    /**
     * 控制台首页
     */
    public function index()
    {
        // 获取核心指标数据
        $cardStats = $this->getCardStats();

        // 获取分类统计
        $categoryStats = $this->getCategoryStats();

        // 获取使用趋势数据
        $usageTrend = $this->getUsageTrend();

        // 获取最近使用记录
        $recentRecords = $this->getRecentRecords();

        // 获取系统状态
        $systemStatus = SystemSetting::getSystemStatus();

        return view('dashboard/index', [
            'cardStats' => $cardStats,
            'categoryStats' => $categoryStats,
            'usageTrend' => $usageTrend,
            'recentRecords' => $recentRecords,
            'systemStatus' => $systemStatus,
            'currentAdmin' => \app\controller\Admin::getCurrentAdmin()
        ]);
    }
    
    /**
     * 获取卡密统计数据
     */
    private function getCardStats()
    {
        $stats = Card::getCardStats();
        $categoryCount = Category::where('status', Category::STATUS_ENABLED)->count();
        
        // 计算环比增长（模拟数据，实际应该从历史数据计算）
        $lastMonthStats = $this->getLastMonthStats();
        
        return [
            'total_cards' => [
                'value' => $stats['total_cards'],
                'growth' => $this->calculateGrowth($stats['total_cards'], $lastMonthStats['total_cards']),
                'icon' => 'fas fa-credit-card',
                'color' => 'primary'
            ],
            'used_cards' => [
                'value' => $stats['used_cards'],
                'growth' => $this->calculateGrowth($stats['used_cards'], $lastMonthStats['used_cards']),
                'icon' => 'fas fa-check-circle',
                'color' => 'success'
            ],
            'disabled_cards' => [
                'value' => $stats['disabled_cards'],
                'growth' => $this->calculateGrowth($stats['disabled_cards'], $lastMonthStats['disabled_cards']),
                'icon' => 'fas fa-ban',
                'color' => 'danger'
            ],
            'unused_cards' => [
                'value' => $stats['unused_cards'],
                'growth' => $this->calculateGrowth($stats['unused_cards'], $lastMonthStats['unused_cards']),
                'icon' => 'fas fa-clock',
                'color' => 'warning'
            ],
            'category_count' => [
                'value' => $categoryCount,
                'growth' => $this->calculateGrowth($categoryCount, $lastMonthStats['category_count']),
                'icon' => 'fas fa-tags',
                'color' => 'info'
            ]
        ];
    }
    
    /**
     * 获取分类统计数据
     */
    private function getCategoryStats()
    {
        $stats = Category::getCategoryStats();
        $colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
        
        $result = [];
        foreach ($stats as $index => $stat) {
            $result[] = [
                'name' => $stat['name'],
                'value' => $stat['total_cards'],
                'color' => $colors[$index % count($colors)]
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取使用趋势数据
     */
    private function getUsageTrend($period = 'day')
    {
        $period = $this->request->param('period', 'day');
        $limit = match($period) {
            'day' => 30,
            'week' => 12,
            'month' => 12,
            'year' => 5,
            default => 30
        };
        
        return Card::getUsageTrend($period, $limit);
    }
    
    /**
     * 获取最近使用记录
     */
    private function getRecentRecords()
    {
        return UsageRecord::getRecentRecords(5);
    }
    
    /**
     * 获取上月统计数据（模拟）
     */
    private function getLastMonthStats()
    {
        // 实际项目中应该从历史数据表或缓存中获取
        return [
            'total_cards' => 850,
            'used_cards' => 420,
            'disabled_cards' => 30,
            'unused_cards' => 400,
            'category_count' => 3
        ];
    }
    
    /**
     * 计算增长率
     */
    private function calculateGrowth($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 1);
    }
    
    /**
     * 获取图表数据API
     */
    public function getChartData()
    {
        $type = $this->request->param('type');
        $period = $this->request->param('period', 'day');
        
        switch ($type) {
            case 'usage_trend':
                $data = $this->getUsageTrend($period);
                break;
            case 'category_stats':
                $data = $this->getCategoryStats();
                break;
            default:
                $data = [];
        }
        
        return json(['code' => 200, 'data' => $data]);
    }
}
