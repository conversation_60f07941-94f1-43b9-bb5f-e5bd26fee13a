{extend name="layout/base" /}

{block name="title"}控制台 - 卡密兑换管理系统{/block}

{block name="style"}
<style>
    /* 统计卡片样式 */
    .stats-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 16px 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        margin-bottom: 12px;
        position: relative;
        z-index: 2;
    }
    
    .stats-value {
        font-size: 28px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 6px;
        line-height: 1;
    }

    .stats-label {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 6px;
        font-weight: 500;
    }

    .stats-growth {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
    }
    
    .growth-positive {
        color: var(--success-color);
    }

    .growth-negative {
        color: var(--error-color);
    }

    /* 图标颜色 */
    .icon-primary { background-color: var(--primary-color); }
    .icon-success { background-color: var(--success-color); }
    .icon-warning { background-color: var(--warning-color); }
    .icon-info { background-color: #13c2c2; }
    .icon-purple { background-color: #722ed1; }

    /* 概览容器样式 */
    .overview-container {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .overview-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .overview-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 0;
        padding: 0;
    }

    .stats-grid .stats-card {
        border-radius: 0;
        border: none;
        border-right: 1px solid var(--border-color);
        box-shadow: none;
        margin: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 16px;
        transition: all 0.3s ease;
    }

    .stats-grid .stats-card:last-child {
        border-right: none;
    }

    .stats-grid .stats-card:hover {
        background-color: #f8f9fa;
        transform: none;
        box-shadow: inset 0 0 0 1px var(--primary-color);
    }
    
    /* 图表容器样式 */
    .chart-container {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .chart-controls {
        display: flex;
        gap: 0.5rem;
    }
    
    .chart-controls .btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* 表格样式 */
    .records-table {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #eee;
    }
    
    .table-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    
    .table td {
        border: none;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }
    
    .table tbody tr {
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-used {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-unused {
        background-color: #fff3cd;
        color: #856404;
    }
    
    /* 快速操作样式 */
    .quick-actions {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s ease;
        background: var(--card-bg);
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .action-item:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
        text-decoration: none;
    }
    
    .action-icon {
        font-size: 28px;
        margin-bottom: 12px;
        color: var(--text-secondary);
        transition: color 0.3s ease;
    }

    .action-item:hover .action-icon {
        color: var(--primary-color);
    }

    .action-label {
        font-size: 14px;
        font-weight: 500;
        text-align: center;
    }

    /* 全宽快速操作样式 */
    .quick-actions-full {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .quick-actions-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .quick-actions-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .actions-grid-full {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 0;
        padding: 0;
    }

    .actions-grid-full .action-item {
        border-radius: 0;
        border: none;
        border-right: 1px solid var(--border-color);
        box-shadow: none;
        margin: 0;
        padding: 24px 16px;
        background: var(--card-bg);
    }

    .actions-grid-full .action-item:last-child {
        border-right: none;
    }

    .actions-grid-full .action-item:hover {
        background-color: #f8f9fa;
        transform: none;
        box-shadow: inset 0 0 0 1px var(--primary-color);
    }

    .actions-grid-full .action-item .action-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }
    
    /* 系统状态样式 */
    .system-status-container {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .system-status-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .system-status-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .system-status {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0;
        padding: 0;
    }
    
    .status-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 20px 24px;
        border-right: 1px solid var(--border-color);
        background-color: var(--card-bg);
        transition: all 0.3s ease;
    }

    .status-item:last-child {
        border-right: none;
    }

    .status-item:hover {
        background-color: #f8f9fa;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .status-success {
        background-color: #28a745;
    }
    
    .status-warning {
        background-color: #ffc107;
    }
    
    .status-danger {
        background-color: #dc3545;
    }
    
    .status-text {
        font-size: 0.9rem;
        color: #495057;
    }
    


    .modern-stats-trend.positive {
        color: var(--success-color);
    }

    .modern-stats-trend.negative {
        color: var(--error-color);
    }

    /* 快速操作按钮样式 */
    .modern-btn.w-100.d-flex.flex-column {
        min-height: 100px;
        text-decoration: none;
    }

    .modern-btn.w-100.d-flex.flex-column:hover {
        text-decoration: none;
    }

    /* 状态指示器 */
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
    }

    .status-indicator.status-success {
        background-color: var(--success-color);
    }

    .status-indicator.status-warning {
        background-color: var(--warning-color);
    }

    .status-indicator.status-error {
        background-color: var(--error-color);
    }

    .positive {
        color: var(--success-color);
    }

    .negative {
        color: var(--error-color);
    }
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">控制台</h1>
        <p class="text-muted mb-0">欢迎回来，这里是您的数据概览</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新数据
        </button>
        <a href="/cards/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 数据概览 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="{$cardStats.total_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.total_cards.raw_value}">{$cardStats.total_cards.value}</div>
        <div class="modern-stats-label">总卡密数</div>
        <div class="modern-stats-trend {$cardStats.total_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.total_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.total_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="{$cardStats.used_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.used_cards.raw_value}">{$cardStats.used_cards.value}</div>
        <div class="modern-stats-label">已使用</div>
        <div class="modern-stats-trend {$cardStats.used_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.used_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.used_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="{$cardStats.unused_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.unused_cards.raw_value}">{$cardStats.unused_cards.value}</div>
        <div class="modern-stats-label">未使用</div>
        <div class="modern-stats-trend {$cardStats.unused_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.unused_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.unused_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="{$cardStats.disabled_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.disabled_cards.raw_value}">{$cardStats.disabled_cards.value}</div>
        <div class="modern-stats-label">已禁用</div>
        <div class="modern-stats-trend {$cardStats.disabled_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.disabled_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.disabled_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="{$cardStats.category_count.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.category_count.raw_value}">{$cardStats.category_count.value}</div>
        <div class="modern-stats-label">分类总数</div>
        <div class="modern-stats-trend {$cardStats.category_count.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.category_count.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.category_count.growth}%
        </div>
    </div>
</div>

<!-- 快速操作中心 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-bolt me-2"></i>
            快速操作
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-3">
            <div class="col-md-2">
                <a href="/cards/generate" class="modern-btn modern-btn-primary w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <span>生成卡密</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/cards" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-credit-card fa-2x mb-2"></i>
                    <span>卡密管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/categories" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-tags fa-2x mb-2"></i>
                    <span>分类管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/content" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <span>内容管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="modern-btn modern-btn-success w-100 d-flex flex-column align-items-center py-3" onclick="showExportModal()">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <span>导出数据</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/settings" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-cog fa-2x mb-2"></i>
                    <span>系统设置</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row g-4 mb-4">
    <!-- 使用趋势图表 -->
    <div class="col-lg-8">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-line me-2"></i>
                    卡密使用趋势
                </h5>
                <div class="d-flex gap-1">
                    <button class="modern-btn modern-btn-primary btn-sm active" data-period="day">日</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="week">周</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="month">月</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="year">年</button>
                </div>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="usageTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类占比图表 -->
    <div class="col-lg-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    分类占比
                </h5>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近使用记录 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-history me-2"></i>
            最近使用记录
        </h5>
        <a href="/usage-records" class="modern-btn modern-btn-outline btn-sm">查看全部</a>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>卡密编号</th>
                        <th>分类</th>
                        <th>使用时间</th>
                        <th>使用IP</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="recentRecords" id="record"}
                    <tr>
                        <td><code class="text-primary">{$record.masked_card_code}</code></td>
                        <td><span class="modern-badge modern-badge-primary">{$record.category_name}</span></td>
                        <td class="text-muted">{$record.used_at}</td>
                        <td class="text-muted">{$record.used_ip|default='未知'}</td>
                        <td>
                            <span class="modern-badge modern-badge-success">
                                <i class="fas fa-check-circle"></i>
                                已使用
                            </span>
                        </td>
                    </tr>
                    {/volist}
                    {empty name="recentRecords"}
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div>暂无使用记录</div>
                            </div>
                        </td>
                    </tr>
                    {/empty}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 系统状态监控 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-server me-2"></i>
            系统状态
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.server_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">服务器状态</div>
                        <small class="text-muted">{$systemStatus.server_status.text}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.database_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">数据库连接</div>
                        <small class="text-muted">{$systemStatus.database_status.text}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.storage_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">存储空间</div>
                        <small class="text-muted">{$systemStatus.storage_status.text}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportType" class="form-label">导出类型</label>
                        <select class="form-select" id="exportType">
                            <option value="all">全部数据</option>
                            <option value="cards">卡密数据</option>
                            <option value="usage">使用记录</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportData()">导出</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    // 图表实例
    let usageTrendChart = null;
    let categoryChart = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initCharts();
        bindEvents();
    });

    // 初始化图表
    function initCharts() {
        try {
            initUsageTrendChart();
            initCategoryChart();
        } catch (error) {
            console.error('图表初始化失败:', error);
        }
    }

    // 初始化使用趋势图表
    function initUsageTrendChart() {
        const ctx = document.getElementById('usageTrendChart').getContext('2d');

        // 从后端获取真实数据
        const backendData = {$usageTrend|json_encode|raw};

        // 如果没有数据，使用默认数据
        let labels = ['暂无数据'];
        let createdData = [0];
        let usedData = [0];

        if (backendData && backendData.labels && backendData.labels.length > 0) {
            labels = backendData.labels;
            createdData = backendData.created || [];
            usedData = backendData.used || [];
        } else {
            // 使用测试数据
            labels = ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'];
            createdData = [10, 15, 8, 20, 12, 18, 25];
            usedData = [5, 10, 6, 15, 8, 12, 20];
        }

        const trendData = {
            labels: labels,
            datasets: [{
                label: '生成卡密',
                data: createdData,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '使用卡密',
                data: usedData,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        };

        try {
            usageTrendChart = new Chart(ctx, {
                type: 'line',
                data: trendData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数量'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        } catch (error) {
            console.error('使用趋势图表创建失败:', error);
        }
    }

    // 初始化分类占比图表
    function initCategoryChart() {
        const ctx = document.getElementById('categoryChart').getContext('2d');

        // 从后端数据构建图表数据
        const backendData = {$categoryStats|json_encode|raw};

        let categoryData = {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        // 处理数据格式
        if (backendData && backendData.labels && backendData.labels.length > 0) {
            categoryData.labels = backendData.labels;
            categoryData.datasets[0].data = backendData.data;
            categoryData.datasets[0].backgroundColor = backendData.colors;
        } else {
            // 使用测试数据
            categoryData.labels = ['Office套件', '游戏道具', '软件授权', '会员服务'];
            categoryData.datasets[0].data = [25, 30, 20, 25];
            categoryData.datasets[0].backgroundColor = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
        }

        try {
            categoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: categoryData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        } catch (error) {
            console.error('分类占比图表创建失败:', error);
        }
    }

    // 绑定事件
    function bindEvents() {
        // 时间维度切换
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // 更新图表数据
                const period = this.dataset.period;
                updateUsageTrendChart(period);
            });
        });
    }

    // 更新使用趋势图表
    function updateUsageTrendChart(period) {
        // 发送AJAX请求获取新数据
        fetch(`/dashboard/chartData?type=usage_trend&period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    const newData = data.data;
                    usageTrendChart.data.labels = newData.labels || [];
                    usageTrendChart.data.datasets[0].data = newData.created || [];
                    usageTrendChart.data.datasets[1].data = newData.used || [];
                    usageTrendChart.update();
                } else {
                    console.error('获取图表数据失败:', data.message);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
            });
    }

    // 显示导出模态框
    function showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }

    // 导出数据
    function exportData() {
        const password = document.getElementById('exportPassword').value;
        const type = document.getElementById('exportType').value;

        if (!password) {
            alert('请输入导出密码');
            return;
        }

        // 这里应该发送AJAX请求到后端
        // 为了演示，我们只是显示一个提示
        alert(`正在导出${type === 'all' ? '全部' : type === 'cards' ? '卡密' : '使用记录'}数据...`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();

        // 清空表单
        document.getElementById('exportForm').reset();
    }
</script>
