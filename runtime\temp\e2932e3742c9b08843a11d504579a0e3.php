<?php /*a:2:{s:41:"F:\linshi\thphp\kmxt\view\logs\index.html";i:1754012886;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1753955066;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --sidebar-bg: #001529;
            --sidebar-text: rgba(255, 255, 255, 0.65);
            --sidebar-active: #1890ff;
            --content-bg: #f0f2f5;
            --card-bg: #ffffff;
            --border-color: #d9d9d9;
            --text-primary: #262626;
            --text-secondary: #8c8c8c;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
            background-color: var(--content-bg);
            padding: 24px;
        }

        .main-content.expanded {
            margin-left: 0;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                <a href="/logs" class="nav-link">
                    <i class="fas fa-list-alt"></i>
                    操作日志
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 24px;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: white;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #f8f9fa;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-danger {
        background: var(--danger-color);
        color: white;
        border-color: var(--danger-color);
    }

    .header-btn.btn-danger:hover {
        background: #d32f2f;
        border-color: #d32f2f;
        color: white;
        box-shadow: 0 3px 10px rgba(255, 77, 79, 0.25);
    }

    .header-btn.btn-success {
        background: var(--success-color);
        color: white;
        border-color: var(--success-color);
    }

    .header-btn.btn-success:hover {
        background: #389e0d;
        border-color: #389e0d;
        color: white;
        box-shadow: 0 3px 10px rgba(82, 196, 26, 0.25);
    }

    /* 统计卡片 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
    }

    .stats-card:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        transform: translateY(-2px);
    }

    .stats-card .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin-bottom: 12px;
    }

    .stats-card .stats-value {
        font-size: 28px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .stats-card .stats-label {
        font-size: 14px;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .stats-card.today .stats-icon { background: linear-gradient(135deg, #1890ff, #40a9ff); }
    .stats-card.week .stats-icon { background: linear-gradient(135deg, #52c41a, #73d13d); }
    .stats-card.month .stats-icon { background: linear-gradient(135deg, #fa8c16, #ffa940); }
    .stats-card.total .stats-icon { background: linear-gradient(135deg, #722ed1, #9254de); }

    /* 筛选区域 */
    .filter-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
    }

    .filter-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filter-title i {
        color: var(--primary-color);
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 16px;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .filter-group label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .filter-group select,
    .filter-group input {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .filter-group select:focus,
    .filter-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .filter-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .filter-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
    }

    .filter-btn.btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .filter-btn.btn-primary:hover {
        background: #0056b3;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .filter-btn.btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .filter-btn.btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
    }

    /* 日志表格 */
    .logs-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .table-header {
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
        background: #f8f9fa;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .table-title i {
        color: var(--primary-color);
    }

    .table-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .table th {
        background: #f8f9fa;
        padding: 16px 20px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
        white-space: nowrap;
    }

    .table td {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;
        color: var(--text-primary);
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* 操作类型标签 */
    .operation-tag {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
    }

    .operation-tag.login { background: #e6f7ff; color: #1890ff; }
    .operation-tag.card { background: #f6ffed; color: #52c41a; }
    .operation-tag.category { background: #fff7e6; color: #fa8c16; }
    .operation-tag.settings { background: #f9f0ff; color: #722ed1; }
    .operation-tag.system { background: #fff1f0; color: #ff4d4f; }
    .operation-tag.default { background: #f5f5f5; color: #8c8c8c; }

    /* 操作按钮 */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        font-size: 14px;
    }

    .action-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .action-btn.btn-info {
        background: #e6f7ff;
        color: #1890ff;
    }

    .action-btn.btn-info:hover {
        background: #1890ff;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    /* 分页 */
    .pagination-wrapper {
        padding: 20px 24px;
        border-top: 1px solid #f0f0f0;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pagination-info {
        font-size: 14px;
        color: var(--text-secondary);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .filter-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
        
        .filter-actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .header-actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .table-header {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
        }
        
        .table-actions {
            justify-content: center;
        }
    }
</style>

<!-- 页面头部 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1 class="page-title">操作日志</h1>
            <p class="page-subtitle">查看和管理系统操作记录</p>
        </div>
        <div class="header-actions">
            <button class="header-btn btn-outline" onclick="exportLogs()">
                <i class="fas fa-download"></i>
                <span>导出日志</span>
            </button>
            <button class="header-btn btn-danger" onclick="showClearModal()">
                <i class="fas fa-trash-alt"></i>
                <span>清理日志</span>
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stats-card today">
        <div class="stats-icon">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stats-value"><?php echo htmlentities((string) $stats['today']); ?></div>
        <div class="stats-label">今日操作</div>
    </div>
    <div class="stats-card week">
        <div class="stats-icon">
            <i class="fas fa-calendar-week"></i>
        </div>
        <div class="stats-value"><?php echo htmlentities((string) $stats['this_week']); ?></div>
        <div class="stats-label">本周操作</div>
    </div>
    <div class="stats-card month">
        <div class="stats-icon">
            <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="stats-value"><?php echo htmlentities((string) $stats['this_month']); ?></div>
        <div class="stats-label">本月操作</div>
    </div>
    <div class="stats-card total">
        <div class="stats-icon">
            <i class="fas fa-database"></i>
        </div>
        <div class="stats-value"><?php echo htmlentities((string) $stats['total']); ?></div>
        <div class="stats-label">总计操作</div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <div class="filter-title">
        <i class="fas fa-filter"></i>
        筛选条件
    </div>
    <form id="filterForm" method="get">
        <div class="filter-grid">
            <div class="filter-group">
                <label>管理员</label>
                <select name="admin_id">
                    <option value="">全部管理员</option>
                    <?php if(is_array($admins) || $admins instanceof \think\Collection || $admins instanceof \think\Paginator): $i = 0; $__LIST__ = $admins;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$admin): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities((string) $admin['id']); ?>" <?php if($filters['admin_id'] == $admin['id']): ?>selected<?php endif; ?>>
                        <?php echo htmlentities((string) $admin['name']); ?> (<?php echo htmlentities((string) $admin['username']); ?>)
                    </option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
            <div class="filter-group">
                <label>操作类型</label>
                <select name="operation_type">
                    <option value="">全部类型</option>
                    <?php if(is_array($operationTypes) || $operationTypes instanceof \think\Collection || $operationTypes instanceof \think\Paginator): $typeKey = 0; $__LIST__ = $operationTypes;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$typeName): $mod = ($typeKey % 2 );++$typeKey;?>
                    <option value="<?php echo htmlentities((string) $typeKey); ?>" <?php if($filters['operation_type'] == $typeKey): ?>selected<?php endif; ?>>
                        <?php echo htmlentities((string) $typeName); ?>
                    </option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
            <div class="filter-group">
                <label>开始日期</label>
                <input type="date" name="start_date" value="<?php echo htmlentities((string) $filters['start_date']); ?>">
            </div>
            <div class="filter-group">
                <label>结束日期</label>
                <input type="date" name="end_date" value="<?php echo htmlentities((string) $filters['end_date']); ?>">
            </div>
            <div class="filter-group">
                <label>关键词</label>
                <input type="text" name="keyword" placeholder="搜索操作描述..." value="<?php echo htmlentities((string) $filters['keyword']); ?>">
            </div>
        </div>
        <div class="filter-actions">
            <button type="submit" class="filter-btn btn-primary">
                <i class="fas fa-search"></i>
                筛选
            </button>
            <button type="button" class="filter-btn btn-outline" onclick="resetFilter()">
                <i class="fas fa-undo"></i>
                重置
            </button>
        </div>
    </form>
</div>

<!-- 日志表格 -->
<div class="logs-table">
    <div class="table-header">
        <h5 class="table-title">
            <i class="fas fa-list-alt"></i>
            操作记录
        </h5>
        <div class="table-actions">
            <button class="filter-btn btn-outline" onclick="selectAll()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button class="filter-btn btn-danger" onclick="batchDelete()" id="batchDeleteBtn" disabled>
                <i class="fas fa-trash"></i>
                批量删除
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th width="40">
                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                    </th>
                    <th width="80">ID</th>
                    <th width="120">管理员</th>
                    <th width="120">操作类型</th>
                    <th>操作描述</th>
                    <th width="120">IP地址</th>
                    <th width="160">操作时间</th>
                    <th width="80">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(is_array($logs) || $logs instanceof \think\Collection || $logs instanceof \think\Paginator): $i = 0; $__LIST__ = $logs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$log): $mod = ($i % 2 );++$i;?>
                <tr>
                    <td>
                        <input type="checkbox" class="log-checkbox" value="<?php echo htmlentities((string) $log['id']); ?>" onchange="updateBatchButton()">
                    </td>
                    <td><?php echo htmlentities((string) $log['id']); ?></td>
                    <td>
                        <?php if($log['admin_username']): ?>
                            <div style="font-weight: 500;"><?php echo htmlentities((string) $log['admin_name']); ?></div>
                            <div style="font-size: 12px; color: #8c8c8c;">@<?php echo htmlentities((string) $log['admin_username']); ?></div>
                        <?php else: ?>
                            <span style="color: #8c8c8c;">系统</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <span class="operation-tag <?php echo htmlentities((string) getOperationClass($log['operation_type'])); ?>">
                            <?php echo htmlentities((string) getOperationName($log['operation_type'])); ?>
                        </span>
                    </td>
                    <td>
                        <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                             title="<?php echo htmlentities((string) $log['operation_desc']); ?>">
                            <?php echo htmlentities((string) $log['operation_desc']); ?>
                        </div>
                    </td>
                    <td>
                        <code style="font-size: 12px; background: #f5f5f5; padding: 2px 6px; border-radius: 4px;">
                            <?php echo htmlentities((string) $log['ip_address']); ?>
                        </code>
                    </td>
                    <td>
                        <div style="font-size: 13px;"><?php echo htmlentities((string) date('m-d H:i',!is_numeric($log['created_at'])? strtotime($log['created_at']) : $log['created_at'])); ?></div>
                        <div style="font-size: 11px; color: #8c8c8c;"><?php echo htmlentities((string) date('Y',!is_numeric($log['created_at'])? strtotime($log['created_at']) : $log['created_at'])); ?></div>
                    </td>
                    <td>
                        <button class="action-btn btn-info" onclick="showLogDetail(<?php echo htmlentities((string) $log['id']); ?>)" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
                <?php endforeach; endif; else: echo "" ;endif; if(empty($logs) || (($logs instanceof \think\Collection || $logs instanceof \think\Paginator ) && $logs->isEmpty())): ?>
                <tr>
                    <td colspan="8" class="text-center" style="padding: 40px; color: #8c8c8c;">
                        <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                        <div>暂无操作日志</div>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    <?php if($logs->total() > 0): ?>
    <div class="pagination-wrapper">
        <div class="pagination-info">
            显示第 <?php echo htmlentities((string) $logs->currentPage()); ?> 页，共 <?php echo htmlentities((string) $logs->lastPage()); ?> 页，
            总计 <?php echo htmlentities((string) $logs->total()); ?> 条记录
        </div>
        <div class="pagination">
            <?php echo htmlentities((string) $logs->render()); ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    操作日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <!-- 详情内容将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 清理日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    清理操作日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">清理方式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="clearType" id="clearDays" value="days" checked>
                        <label class="form-check-label" for="clearDays">
                            清理指定天数前的日志
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="clearType" id="clearAll" value="all">
                        <label class="form-check-label" for="clearAll">
                            清空所有日志
                        </label>
                    </div>
                </div>
                <div class="mb-3" id="daysInput">
                    <label for="clearDaysValue" class="form-label">保留天数</label>
                    <input type="number" class="form-control" id="clearDaysValue" value="30" min="1" max="365">
                    <div class="form-text">将删除指定天数前的所有日志记录</div>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作不可恢复，请谨慎操作！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmClearLogs()">
                    <i class="fas fa-trash-alt"></i>
                    确认清理
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出日志模态框 -->
<div class="modal fade" id="exportLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download"></i>
                    导出操作日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label class="form-label">导出格式</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatCsv" value="csv" checked>
                            <label class="form-check-label" for="formatCsv">
                                CSV格式 (Excel可直接打开)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatJson" value="json">
                            <label class="form-check-label" for="formatJson">
                                JSON格式 (程序处理)
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">筛选条件</label>
                        <div class="form-text mb-2">将使用当前页面的筛选条件进行导出</div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            最多导出10,000条记录
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="confirmExportLogs()">
                    <i class="fas fa-download"></i>
                    开始导出
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期选择器默认值
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        // 监听清理方式选择
        document.querySelectorAll('input[name="clearType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const daysInput = document.getElementById('daysInput');
                if (this.value === 'days') {
                    daysInput.style.display = 'block';
                } else {
                    daysInput.style.display = 'none';
                }
            });
        });
    });

    // 全选/取消全选
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const checkboxes = document.querySelectorAll('.log-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        updateBatchButton();
    }

    // 选择全部
    function selectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        selectAllCheckbox.checked = true;
        toggleSelectAll();
    }

    // 更新批量操作按钮状态
    function updateBatchButton() {
        const checkedBoxes = document.querySelectorAll('.log-checkbox:checked');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');

        if (checkedBoxes.length > 0) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.innerHTML = `<i class="fas fa-trash"></i> 批量删除 (${checkedBoxes.length})`;
        } else {
            batchDeleteBtn.disabled = true;
            batchDeleteBtn.innerHTML = '<i class="fas fa-trash"></i> 批量删除';
        }
    }

    // 批量删除
    function batchDelete() {
        const checkedBoxes = document.querySelectorAll('.log-checkbox:checked');
        if (checkedBoxes.length === 0) {
            showToast('请选择要删除的日志', 'warning');
            return;
        }

        if (!confirm(`确定要删除选中的 ${checkedBoxes.length} 条日志吗？此操作不可恢复！`)) {
            return;
        }

        const ids = Array.from(checkedBoxes).map(cb => cb.value);

        fetch('/logs/batchDelete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('删除失败，请稍后重试', 'error');
        });
    }

    // 显示日志详情
    function showLogDetail(id) {
        fetch(`/logs/detail?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                displayLogDetail(data.data);
                const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
                modal.show();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('获取详情失败', 'error');
        });
    }

    // 显示日志详情内容
    function displayLogDetail(log) {
        const content = document.getElementById('logDetailContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">日志ID</label>
                        <div>${log.id}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">操作管理员</label>
                        <div>${log.admin_name || '系统'} ${log.admin_username ? '(@' + log.admin_username + ')' : ''}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">操作类型</label>
                        <div><span class="operation-tag ${getOperationClass(log.operation_type)}">${getOperationName(log.operation_type)}</span></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">IP地址</label>
                        <div><code>${log.ip_address}</code></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">操作时间</label>
                        <div>${log.created_at}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">用户代理</label>
                        <div style="font-size: 12px; word-break: break-all;">${log.user_agent || '未知'}</div>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label fw-bold">操作描述</label>
                <div class="p-2 bg-light rounded">${log.operation_desc}</div>
            </div>
            ${log.request_data_formatted ? `
            <div class="mb-3">
                <label class="form-label fw-bold">请求数据</label>
                <pre class="bg-light p-3 rounded" style="font-size: 12px; max-height: 200px; overflow-y: auto;">${log.request_data_formatted}</pre>
            </div>
            ` : ''}
        `;
    }

    // 显示清理模态框
    function showClearModal() {
        const modal = new bootstrap.Modal(document.getElementById('clearLogsModal'));
        modal.show();
    }

    // 确认清理日志
    function confirmClearLogs() {
        const clearType = document.querySelector('input[name="clearType"]:checked').value;
        const days = clearType === 'days' ? document.getElementById('clearDaysValue').value : 0;

        const confirmText = clearType === 'days'
            ? `确定要清理 ${days} 天前的日志吗？`
            : '确定要清空所有日志吗？';

        if (!confirm(confirmText + ' 此操作不可恢复！')) {
            return;
        }

        fetch('/logs/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ days: parseInt(days) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('清理失败，请稍后重试', 'error');
        });

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('clearLogsModal'));
        modal.hide();
    }

    // 显示导出模态框
    function exportLogs() {
        const modal = new bootstrap.Modal(document.getElementById('exportLogsModal'));
        modal.show();
    }

    // 确认导出日志
    function confirmExportLogs() {
        const format = document.querySelector('input[name="format"]:checked').value;
        const currentUrl = new URL(window.location);
        const params = new URLSearchParams(currentUrl.search);
        params.set('format', format);

        // 构建导出URL
        const exportUrl = '/logs/export?' + params.toString();

        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportLogsModal'));
        modal.hide();

        showToast('导出已开始，请稍候...', 'info');
    }

    // 重置筛选
    function resetFilter() {
        window.location.href = '/logs';
    }

    // 获取操作类型样式类
    function getOperationClass(type) {
        if (type.includes('login') || type.includes('logout')) return 'login';
        if (type.includes('card')) return 'card';
        if (type.includes('category')) return 'category';
        if (type.includes('settings')) return 'settings';
        if (type.includes('log') || type.includes('cache') || type.includes('database')) return 'system';
        return 'default';
    }

    // 获取操作类型名称
    function getOperationName(type) {
        const types = {
            'login': '用户登录',
            'logout': '用户登出',
            'card_generate': '生成卡密',
            'card_edit': '编辑卡密',
            'card_delete': '删除卡密',
            'card_export': '导出卡密',
            'category_create': '创建分类',
            'category_edit': '编辑分类',
            'category_delete': '删除分类',
            'content_edit': '编辑内容',
            'settings_update': '更新设置',
            'log_delete': '删除日志',
            'log_clear': '清理日志',
            'cache_clear': '清除缓存',
            'database_optimize': '优化数据库'
        };
        return types[type] || type;
    }

    // Toast 通知
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }
</script>

<!-- Toast 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
</style>


        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // 点击外部关闭移动端侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('expanded');
                } else {
                    mainContent.classList.remove('expanded');
                }
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });

        // 设置导航激活状态
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    
</body>
</html>
