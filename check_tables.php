<?php
require_once 'vendor/autoload.php';

$app = new think\App();
$app->initialize();

use think\facade\Db;

try {
    echo "检查数据库连接...\n";
    $connection = Db::connect();
    echo "✅ 数据库连接成功\n\n";

    echo "📊 当前数据库中的表:\n";
    $tables = Db::query('SHOW TABLES');
    
    if (empty($tables)) {
        echo "❌ 数据库中没有任何表\n";
    } else {
        foreach($tables as $table) {
            $tableName = array_values($table)[0];
            echo "  - {$tableName}\n";
        }
    }
    
    echo "\n🔍 检查管理员相关的表:\n";
    
    // 检查 km_admins 表
    $kmAdminsExists = Db::query("SHOW TABLES LIKE 'km_admins'");
    if (!empty($kmAdminsExists)) {
        echo "✅ km_admins 表存在\n";
        $count = Db::name('km_admins')->count();
        echo "   记录数: {$count}\n";
        
        if ($count > 0) {
            $admins = Db::name('km_admins')->select();
            foreach ($admins as $admin) {
                echo "   - ID: {$admin['id']}, 用户名: {$admin['username']}, 姓名: {$admin['name']}\n";
            }
        }
    } else {
        echo "❌ km_admins 表不存在\n";
    }
    
    // 检查 admins 表
    $adminsExists = Db::query("SHOW TABLES LIKE 'admins'");
    if (!empty($adminsExists)) {
        echo "✅ admins 表存在\n";
        $count = Db::name('admins')->count();
        echo "   记录数: {$count}\n";
    } else {
        echo "❌ admins 表不存在\n";
    }
    
    // 检查 km_admin_users 表
    $kmAdminUsersExists = Db::query("SHOW TABLES LIKE 'km_admin_users'");
    if (!empty($kmAdminUsersExists)) {
        echo "✅ km_admin_users 表存在\n";
        $count = Db::name('km_admin_users')->count();
        echo "   记录数: {$count}\n";
        
        if ($count > 0) {
            $admins = Db::name('km_admin_users')->select();
            foreach ($admins as $admin) {
                echo "   - ID: {$admin['id']}, 用户名: {$admin['username']}, 邮箱: {$admin['email']}\n";
            }
        }
    } else {
        echo "❌ km_admin_users 表不存在\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
?>
