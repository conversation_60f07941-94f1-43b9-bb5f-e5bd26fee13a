<?php

namespace app\model;

use think\Model;

/**
 * 卡密模型
 */
class Card extends Model
{
    // 设置表名
    protected $table = 'km_cards';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'card_code'   => 'string',
        'category_id' => 'int',
        'status'      => 'int',
        'content'     => 'string',
        'title'       => 'string',
        'sort_order'  => 'int',
        'used_at'     => 'datetime',
        'used_ip'     => 'string',
        'expire_at'   => 'datetime',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 状态常量
    const STATUS_UNUSED = 0;    // 未使用
    const STATUS_USED = 1;      // 已使用
    const STATUS_DISABLED = 2;  // 已禁用
    
    /**
     * 状态获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_UNUSED => '未使用',
            self::STATUS_USED => '已使用',
            self::STATUS_DISABLED => '已禁用',
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 卡密编号获取器（部分隐藏）
     */
    public function getMaskedCardCodeAttr($value, $data)
    {
        $cardCode = $data['card_code'] ?? '';
        if (strlen($cardCode) <= 8) {
            return $cardCode;
        }
        return substr($cardCode, 0, 4) . '****' . substr($cardCode, -4);
    }
    
    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
    
    /**
     * 关联使用记录
     */
    public function usageRecords()
    {
        return $this->hasMany(UsageRecord::class, 'card_id', 'id');
    }
    
    /**
     * 获取卡密统计数据
     */
    public static function getCardStats()
    {
        try {
            $stats = \think\facade\Db::table('km_cards')
                ->field([
                    'COUNT(*) as total_cards',
                    'SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as unused_cards',
                    'SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as used_cards',
                    'SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as disabled_cards'
                ])
                ->find();

            return $stats ? $stats : [
                'total_cards' => 0,
                'unused_cards' => 0,
                'used_cards' => 0,
                'disabled_cards' => 0
            ];
        } catch (\Exception $e) {
            return [
                'total_cards' => 0,
                'unused_cards' => 0,
                'used_cards' => 0,
                'disabled_cards' => 0
            ];
        }
    }
    
    /**
     * 获取使用趋势数据
     */
    public static function getUsageTrend($period = 'day', $limit = 30)
    {
        $dateFormat = match($period) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };
        
        // 生成卡密趋势
        $createdTrend = self::field([
            "DATE_FORMAT(created_at, '{$dateFormat}') as date",
            'COUNT(*) as count'
        ])
        ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime("-{$limit} {$period}s")))
        ->group("DATE_FORMAT(created_at, '{$dateFormat}')")
        ->order('date', 'asc')
        ->select()
        ->toArray();
        
        // 使用卡密趋势
        $usedTrend = self::field([
            "DATE_FORMAT(used_at, '{$dateFormat}') as date",
            'COUNT(*) as count'
        ])
        ->where('status', self::STATUS_USED)
        ->where('used_at', '>=', date('Y-m-d H:i:s', strtotime("-{$limit} {$period}s")))
        ->group("DATE_FORMAT(used_at, '{$dateFormat}')")
        ->order('date', 'asc')
        ->select()
        ->toArray();
        
        return [
            'created' => $createdTrend,
            'used' => $usedTrend
        ];
    }
    
    /**
     * 生成唯一卡密编号
     */
    public static function generateUniqueCardCode($length = 16)
    {
        do {
            $cardCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, $length));
        } while (self::where('card_code', $cardCode)->find());
        
        return $cardCode;
    }
}
