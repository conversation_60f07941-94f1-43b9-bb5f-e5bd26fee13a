<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Session;

/**
 * 管理员登录控制器
 */
class Admin extends BaseController
{
    /**
     * 登录页面
     */
    public function login()
    {
        // 如果已经登录，跳转到后台
        if (Session::has('admin_id')) {
            return redirect('/dashboard');
        }

        if ($this->request->isPost()) {
            return $this->doLogin();
        }

        return view('admin/login');
    }

    /**
     * 执行登录
     */
    private function doLogin()
    {
        $username = $this->request->post('username', '');
        $password = $this->request->post('password', '');

        if (empty($username) || empty($password)) {
            return json(['code' => 400, 'message' => '用户名和密码不能为空']);
        }

        try {
            // 查找管理员
            $admin = Db::name('admins')->where('username', $username)->find();

            if (!$admin) {
                return json(['code' => 401, 'message' => '用户名或密码错误']);
            }

            // 验证密码
            if (!password_verify($password, $admin['password'])) {
                return json(['code' => 401, 'message' => '用户名或密码错误']);
            }

            // 检查账号状态
            if ($admin['status'] != 1) {
                return json(['code' => 403, 'message' => '账号已被禁用']);
            }

            // 设置登录会话
            Session::set('admin_id', $admin['id']);
            Session::set('admin_username', $admin['username']);
            Session::set('admin_name', $admin['name']);

            // 更新最后登录时间
            Db::name('admins')->where('id', $admin['id'])->update([
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => $this->request->ip(),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return json(['code' => 200, 'message' => '登录成功', 'redirect' => '/dashboard']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '登录失败，请稍后重试']);
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        Session::clear();
        return redirect('/login');
    }

    /**
     * 创建默认管理员账号
     */
    public function createDefaultAdmin()
    {
        try {
            // 检查是否已存在管理员
            $exists = Db::name('admins')->where('username', 'admin')->find();
            if ($exists) {
                return json(['code' => 400, 'message' => '默认管理员已存在']);
            }

            // 创建管理员表
            $this->createAdminTable();

            // 创建默认管理员
            $adminData = [
                'username' => 'admin',
                'password' => password_hash('admin', PASSWORD_DEFAULT),
                'name' => '系统管理员',
                'email' => '<EMAIL>',
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Db::name('admins')->insert($adminData);

            return json(['code' => 200, 'message' => '默认管理员创建成功，用户名：admin，密码：admin']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 创建管理员表
     */
    private function createAdminTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `km_admins` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
          `username` varchar(50) NOT NULL COMMENT '用户名',
          `password` varchar(255) NOT NULL COMMENT '密码',
          `name` varchar(100) NOT NULL COMMENT '姓名',
          `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
          `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
          `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
          `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
        ";

        Db::execute($sql);
    }

    /**
     * 检查登录状态
     */
    public static function checkLogin()
    {
        return Session::has('admin_id');
    }

    /**
     * 获取当前登录管理员信息
     */
    public static function getCurrentAdmin()
    {
        if (!self::checkLogin()) {
            return null;
        }

        return [
            'id' => Session::get('admin_id'),
            'username' => Session::get('admin_username'),
            'name' => Session::get('admin_name')
        ];
    }
}
