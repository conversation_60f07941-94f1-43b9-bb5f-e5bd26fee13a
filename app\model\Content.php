<?php

namespace app\model;

use think\Model;

class Content extends Model
{
    // 设置表名
    protected $table = 'km_contents';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'title'       => 'string',
        'content'     => 'string',
        'category_id' => 'int',
        'sort_order'  => 'int',
        'status'      => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用
    
    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
    
    /**
     * 关联卡密
     */
    public function cards()
    {
        return $this->hasMany(Card::class, 'content_id', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取可用的内容列表
     */
    public static function getAvailableContents($categoryId = null)
    {
        $query = self::where('status', self::STATUS_ENABLED);
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        return $query->order('sort_order', 'desc')
                    ->order('created_at', 'desc')
                    ->select();
    }
    
    /**
     * 获取内容统计
     */
    public static function getStats()
    {
        $total = self::count();
        $enabled = self::where('status', self::STATUS_ENABLED)->count();
        $disabled = self::where('status', self::STATUS_DISABLED)->count();
        
        // 统计每个分类的内容数量
        $categoryStats = self::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field(['cat.name as category_name', 'COUNT(c.id) as count'])
            ->where('c.status', self::STATUS_ENABLED)
            ->group('c.category_id')
            ->select();
        
        return [
            'total' => $total,
            'enabled' => $enabled,
            'disabled' => $disabled,
            'category_stats' => $categoryStats
        ];
    }
}
