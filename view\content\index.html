{extend name="layout/base" /}

{block name="title"}内容管理{/block}

{block name="content"}
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 32px;
    }

    .page-title-main {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .header-btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .header-btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .header-btn.btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .header-btn.btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.15);
    }

    .header-btn.btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .header-btn.btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .header-btn.btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .header-btn.btn-success:hover {
        background: #218838;
        border-color: #218838;
        color: white;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.25);
    }

    .header-btn.btn-warning {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .header-btn.btn-warning:hover {
        background: #5a6268;
        border-color: #5a6268;
        color: white;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25);
    }

    .header-btn i {
        font-size: 11px;
    }

    /* 统计卡片 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 110px;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-color);
        border-radius: 12px 12px 0 0;
    }

    .stat-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .stat-icon.blue {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: white;
    }

    .stat-icon.green {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        color: white;
    }

    .stat-icon.orange {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
        color: white;
    }

    .stat-icon.red {
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
        color: white;
    }

    .stat-label {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #262626;
        line-height: 1;
        margin: 0;
    }

    /* 筛选工具栏 */
    .toolbar {
        background: white;
        padding: 20px 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        margin-bottom: 24px;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
    }

    .btn i {
        font-size: 11px;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    /* 筛选表单 */
    .filter-form {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: nowrap;
    }

    .form-select, .form-input {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 13px;
        transition: all 0.2s ease;
    }

    .form-select:focus, .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-select {
        min-width: 140px;
        background: white;
        cursor: pointer;
    }

    .form-input {
        min-width: 220px;
        flex: 1;
    }

    /* 分类层级样式 */
    .form-select option {
        padding: 6px 8px;
        line-height: 1.5;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .category-option-level-1 {
        font-weight: 700;
        color: #1890ff !important;
        background-color: #f0f9ff !important;
    }

    .category-option-level-2 {
        color: #52c41a !important;
        font-weight: 600;
        background-color: #f6ffed !important;
    }

    .category-option-level-3 {
        color: #fa8c16 !important;
        font-weight: 500;
        background-color: #fff7e6 !important;
        font-size: 13px;
    }

    /* 选中状态样式 */
    .form-select option:checked {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    /* 增强下拉框样式 */
    .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 8px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 32px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-select:hover {
        border-color: #40a9ff;
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
    }

    /* 分类选择框特殊样式 */
    select[name="category_id"] {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }



    /* 筛选标签样式 */
    .filter-label {
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
        margin-right: 4px;
        white-space: nowrap;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    /* 内容表格 */
    .content-table {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        overflow: hidden;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .table th {
        background: #fafafa;
        padding: 16px 12px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        font-size: 13px;
    }

    .table td {
        padding: 16px 12px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    /* 状态标签 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
    }

    .status-unused {
        background: #e6f7ff;
        color: #1890ff;
    }

    .status-used {
        background: #f6ffed;
        color: #52c41a;
    }

    .status-disabled {
        background: #fff2e8;
        color: #fa8c16;
    }

    /* 内容预览 */
    .content-preview {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .content-preview:hover {
        color: var(--primary-color);
    }

    /* 操作按钮 */
    .action-btn {
        padding: 4px 8px;
        font-size: 11px;
        border-radius: 4px;
        margin-right: 4px;
    }

    /* 分页 */
    .pagination-wrapper {
        padding: 20px 24px;
        background: white;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* 响应式 */
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column !important;
            align-items: stretch !important;
            gap: 16px;
        }

        .header-actions {
            justify-content: center;
            flex-wrap: wrap;
        }

        .filter-form {
            flex-wrap: wrap;
            gap: 8px;
        }

        .form-select {
            min-width: 120px;
            flex: 1;
        }

        .form-input {
            min-width: 180px;
            flex: 2;
        }

        .btn {
            flex-shrink: 0;
            white-space: nowrap;
        }
    }

    @media (max-width: 480px) {
        .filter-form {
            flex-direction: column;
            align-items: stretch;
        }

        .form-select,
        .form-input,
        .btn {
            width: 100%;
            min-width: auto;
        }
    }

    /* 排序控件样式 */
    .sort-controls {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .sort-controls .btn {
        padding: 2px 6px;
        font-size: 10px;
        line-height: 1;
        border-radius: 3px;
    }

    .sort-input {
        text-align: center;
        font-size: 12px;
    }

    .sort-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    }

    .content-preview {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .content-preview:hover {
        color: var(--primary-color);
        background-color: rgba(99, 102, 241, 0.05);
        border-radius: 4px;
        padding: 2px 4px;
    }

    /* 内容标题单元格样式 */
    .content-title-cell {
        min-width: 200px;
    }

    .content-title {
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 4px;
        border-radius: 6px;
    }

    .content-title:hover {
        background-color: rgba(99, 102, 241, 0.05);
        transform: translateY(-1px);
    }

    .content-title .fw-bold {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 4px;
    }

    .content-title .small {
        font-size: 11px;
        opacity: 0.8;
    }

    .sortable-row {
        transition: all 0.3s ease;
    }

    .sortable-row:hover {
        background-color: rgba(99, 102, 241, 0.02);
    }

    /* 内容管理页面专用样式 */
    .modern-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    @media (max-width: 1200px) {
        .modern-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .sort-controls {
            flex-direction: row;
        }
    }

    @media (max-width: 768px) {
        .sort-input {
            width: 60px !important;
        }
    }
</style>

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">内容管理</h1>
        <p class="text-muted mb-0">管理卡密的兑换内容，支持批量编辑、排序和导出功能</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="batchEditContent()">
            <i class="fas fa-edit"></i>
            批量编辑
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="clearSelectedContent()">
            <i class="fas fa-eraser"></i>
            清空内容
        </button>
        <a href="/content/export{if condition='$_GET'}?{$_SERVER.QUERY_STRING}{/if}" class="modern-btn modern-btn-primary btn-sm">
            <i class="fas fa-download"></i>
            导出
        </a>
    </div>
</div>

<!-- 统计概况 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-file-alt"></i>
        </div>
        <div class="modern-stats-value">{$stats.total}</div>
        <div class="modern-stats-label">总卡密数</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="modern-stats-value">{$stats.has_content}</div>
        <div class="modern-stats-label">有内容</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        <div class="modern-stats-value">{$stats.no_content}</div>
        <div class="modern-stats-label">无内容</div>
    </div>
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="modern-stats-value">{$stats.expired}</div>
        <div class="modern-stats-label">已过期</div>
    </div>
</div>

<!-- 筛选工具栏 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h5>
        <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="resetFilters()">
            <i class="fas fa-undo"></i>
            重置
        </button>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">分类</label>
                        <select name="category_id" class="modern-form-control">
                            <option value="">全部分类</option>
                            {volist name="categories" id="category"}
                            <option value="{$category.id}"
                                    class="category-option-level-{$category.level}"
                                    {if $filters.category_id == $category.id}selected{/if}>
                                {switch name="category.level"}
                                    {case value="1"}
                                        ■ {$category.name}
                                    {/case}
                                    {case value="2"}
                                        &nbsp;&nbsp;&nbsp;&nbsp;▶ {$category.name}
                                    {/case}
                                    {case value="3"}
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;● {$category.name}
                                    {/case}
                                {/switch}
                            </option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">状态</label>
                        <select name="status" class="modern-form-control">
                            <option value="">全部状态</option>
                            <option value="0" {if $filters.status === '0'}selected{/if}>未使用</option>
                            <option value="1" {if $filters.status === '1'}selected{/if}>已使用</option>
                            <option value="2" {if $filters.status === '2'}selected{/if}>已禁用</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">内容状态</label>
                        <select name="has_content" class="modern-form-control">
                            <option value="">全部</option>
                            <option value="1" {if $filters.has_content === '1'}selected{/if}>有内容</option>
                            <option value="0" {if $filters.has_content === '0'}selected{/if}>无内容</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">搜索关键词</label>
                        <input type="text" name="keyword" class="modern-form-control" placeholder="卡密编号或内容..."
                               value="{$filters.keyword}">
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">&nbsp;</label>
                        <button type="submit" class="modern-btn modern-btn-primary w-100">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 内容列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            内容列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="selectAll()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="batchUpdateSort()">
                <i class="fas fa-sort"></i>
                批量排序
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>内容标题</th>
                        <th>分类</th>
                        <th>状态</th>
                        <th>排序</th>
                        <th>创建时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="contentTableBody">
                    {volist name="contents" id="content"}
                    <tr data-id="{$content.id}" class="sortable-row">
                        <td>
                            <input type="checkbox" class="content-checkbox" value="{$content.id}">
                        </td>
                        <td>
                            <div class="content-title-cell">
                                <div class="content-title" onclick="showContentModal('{$content.id}', '{$content.title|default=$content.card_code}', `{$content.content|raw}`)"
                                     title="点击查看完整内容">
                                    {if $content.content}
                                        <div class="fw-bold text-dark mb-1">
                                            {php}
                                                $title = $content['title'] ?: mb_substr(strip_tags($content['content']), 0, 30, 'utf-8') ?: '无标题';
                                                echo htmlentities($title);
                                                if (!$content['title'] && mb_strlen($content['content'], 'utf-8') > 30) {
                                                    echo '...';
                                                }
                                            {/php}
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-credit-card me-1"></i>
                                            {$content.card_code}
                                        </div>
                                    {else}
                                        <div class="text-muted fst-italic">
                                            <i class="fas fa-file-alt me-1"></i>
                                            暂无内容
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-credit-card me-1"></i>
                                            {$content.card_code}
                                        </div>
                                    {/if}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="modern-badge modern-badge-primary">{$content.category_name}</span>
                        </td>
                        <td>
                            {switch name="content.status"}
                                {case value="0"}
                                    <span class="modern-badge modern-badge-warning">
                                        <i class="fas fa-clock"></i>
                                        未使用
                                    </span>
                                {/case}
                                {case value="1"}
                                    <span class="modern-badge modern-badge-success">
                                        <i class="fas fa-check-circle"></i>
                                        已使用
                                    </span>
                                {/case}
                                {case value="2"}
                                    <span class="modern-badge modern-badge-error">
                                        <i class="fas fa-ban"></i>
                                        已禁用
                                    </span>
                                {/case}
                            {/switch}
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <input type="number" class="form-control form-control-sm sort-input"
                                       value="{$content.sort_order|default=0}"
                                       min="0" max="9999"
                                       style="width: 70px;"
                                       onchange="updateSort({$content.id}, this.value)"
                                       data-original="{$content.sort_order|default=0}">
                                <div class="sort-controls">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveUp({$content.id})" title="上移">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="moveDown({$content.id})" title="下移">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td class="text-muted">
                            <small>{$content.created_at|date='Y-m-d H:i'}</small>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="/content/edit?id={$content.id}" class="modern-btn modern-btn-outline btn-sm" title="编辑内容">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="duplicateContent({$content.id})" title="复制内容">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="deleteContent({$content.id})" title="删除内容">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {/volist}
                    {empty name="contents"}
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div class="h5">暂无内容数据</div>
                                <p>当前筛选条件下没有找到相关内容</p>
                                <button class="modern-btn modern-btn-primary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i>
                                    重置筛选
                                </button>
                            </div>
                        </td>
                    </tr>
                    {/empty}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 分页 -->
    {if condition="$contents->hasPages()"}
    <div class="modern-card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                显示第 {$contents->currentPage()} 页，共 {$contents->lastPage()} 页，总计 {$contents->total()} 条记录
            </div>
            <div>
                {$contents->render()|raw}
            </div>
        </div>
    </div>
    {/if}
</div>

<!-- 内容详情模态框 -->
<div id="contentModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3 id="modalTitle">内容详情</h3>
            <span class="close" onclick="closeContentModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>内容：</label>
                <textarea id="modalContent" rows="8" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" readonly></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeContentModal()">关闭</button>
            <button class="modern-btn modern-btn-primary" onclick="editFromModal()">编辑</button>
        </div>
    </div>
</div>

<!-- 批量编辑模态框 -->
<div id="batchEditModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3>批量编辑内容</h3>
            <span class="close" onclick="closeBatchEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="batchEditForm">
                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">新内容：</label>
                    <textarea name="content" rows="6" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"
                              placeholder="请输入要批量设置的内容..." required></textarea>
                </div>
                <div class="form-group" style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">过期时间（可选）：</label>
                    <input type="datetime-local" name="expire_at" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div class="selected-info" style="background: #f8f9fa; padding: 12px; border-radius: 4px; margin-bottom: 16px;">
                    已选择 <span id="selectedCount">0</span> 个卡密
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeBatchEditModal()">取消</button>
            <button class="modern-btn modern-btn-primary" onclick="submitBatchEdit()">确认更新</button>
        </div>
    </div>
</div>

<script>
    let currentContentId = null;

    // 全选/取消全选
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.content-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateSelectedCount();
    }

    // 更新选中数量
    function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        const count = checkboxes.length;

        const selectedCountElement = document.getElementById('selectedCount');
        if (selectedCountElement) {
            selectedCountElement.textContent = count;
        }

        return count;
    }

    // 监听复选框变化
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('content-checkbox')) {
            updateSelectedCount();

            // 更新全选状态
            const checkboxes = document.querySelectorAll('.content-checkbox');
            const checkedBoxes = document.querySelectorAll('.content-checkbox:checked');
            const selectAll = document.getElementById('selectAll');

            if (checkedBoxes.length === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (checkedBoxes.length === checkboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }
        }
    });

    // 显示内容详情模态框
    function showContentModal(id, cardCode, content) {
        currentContentId = id;
        document.getElementById('modalTitle').textContent = `内容详情 - ${cardCode}`;
        document.getElementById('modalContent').value = content || '';
        document.getElementById('contentModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // 关闭内容详情模态框
    function closeContentModal() {
        document.getElementById('contentModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        currentContentId = null;
    }

    // 从模态框编辑
    function editFromModal() {
        if (currentContentId) {
            window.location.href = `/content/edit?id=${currentContentId}`;
        }
    }

    // 批量编辑内容
    function batchEditContent() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要编辑的卡密', 'warning');
            return;
        }

        updateSelectedCount();
        document.getElementById('batchEditModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // 关闭批量编辑模态框
    function closeBatchEditModal() {
        document.getElementById('batchEditModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        document.getElementById('batchEditForm').reset();
    }

    // 提交批量编辑
    function submitBatchEdit() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要编辑的卡密', 'warning');
            return;
        }

        const form = document.getElementById('batchEditForm');
        const formData = new FormData(form);

        if (!formData.get('content').trim()) {
            showToast('请输入内容', 'warning');
            return;
        }

        const loadingToast = showToast('正在批量更新...', 'info');

        fetch('/content/batchEdit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(','),
                content: formData.get('content'),
                expire_at: formData.get('expire_at')
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast(data.message, 'success');
                closeBatchEditModal();
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('批量更新失败，请稍后重试', 'error');
            console.error('Error:', error);
        });
    }

    // 清空选中内容
    function clearSelectedContent() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要清空的卡密', 'warning');
            return;
        }

        if (!confirm(`确定要清空 ${selectedIds.length} 个卡密的内容吗？此操作不可撤销。`)) {
            return;
        }

        const loadingToast = showToast('正在清空内容...', 'info');

        fetch('/content/clearContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(',')
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('清空失败，请稍后重试', 'error');
            console.error('Error:', error);
        });
    }

    // 获取选中的ID列表
    function getSelectedIds() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    // 重置筛选条件
    function resetFilters() {
        const form = document.querySelector('.filter-form');
        const categorySelect = form.querySelector('select[name="category_id"]');
        const statusSelect = form.querySelector('select[name="status"]');
        const keywordInput = form.querySelector('input[name="keyword"]');

        // 检查是否有筛选条件
        const hasFilters = categorySelect.value || statusSelect.value || keywordInput.value.trim();

        if (!hasFilters) {
            showToast('当前没有筛选条件需要重置', 'info');
            return;
        }

        // 重置表单
        categorySelect.value = '';
        statusSelect.value = '';
        keywordInput.value = '';

        // 显示重置提示
        showToast('筛选条件已重置', 'success');

        // 提交表单以应用重置
        setTimeout(() => {
            form.submit();
        }, 500);
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);

        return toast;
    }

    function hideToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 排序功能
    function updateSort(id, sortOrder) {
        fetch('/content/updateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                id: id,
                sort_order: sortOrder
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast('排序更新成功', 'success');
            } else {
                showToast(data.message || '排序更新失败', 'error');
                // 恢复原值
                const input = document.querySelector(`input[onchange*="${id}"]`);
                if (input) {
                    input.value = input.dataset.original;
                }
            }
        })
        .catch(error => {
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 上移
    function moveUp(id) {
        const input = document.querySelector(`input[onchange*="${id}"]`);
        if (input) {
            const currentValue = parseInt(input.value) || 0;
            const newValue = currentValue + 1;
            input.value = newValue;
            updateSort(id, newValue);
        }
    }

    // 下移
    function moveDown(id) {
        const input = document.querySelector(`input[onchange*="${id}"]`);
        if (input) {
            const currentValue = parseInt(input.value) || 0;
            const newValue = Math.max(0, currentValue - 1);
            input.value = newValue;
            updateSort(id, newValue);
        }
    }

    // 批量更新排序
    function batchUpdateSort() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showToast('请先选择要排序的内容', 'warning');
            return;
        }

        const startSort = prompt('请输入起始排序号（数字越大排序越靠前）:', '100');
        if (startSort === null) return;

        const startSortNum = parseInt(startSort);
        if (isNaN(startSortNum) || startSortNum < 0) {
            showToast('请输入有效的排序号', 'error');
            return;
        }

        const loadingToast = showToast('正在批量更新排序...', 'info');

        fetch('/content/batchUpdateSort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: selectedIds.join(','),
                start_sort: startSortNum
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('批量排序更新成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '批量排序更新失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 复制内容
    function duplicateContent(id) {
        if (!confirm('确定要复制这个内容吗？')) {
            return;
        }

        const loadingToast = showToast('正在复制内容...', 'info');

        fetch('/content/duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                id: id
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('内容复制成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '内容复制失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 删除内容
    function deleteContent(id) {
        if (!confirm('确定要删除这个内容吗？此操作将清空该卡密的内容。')) {
            return;
        }

        const loadingToast = showToast('正在删除内容...', 'info');

        fetch('/content/clearContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ids: id
            })
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);
            if (data.code === 200) {
                showToast('内容删除成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '内容删除失败', 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 全选功能
    function selectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        selectAllCheckbox.checked = true;
        toggleSelectAll();
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const contentModal = document.getElementById('contentModal');
        const batchEditModal = document.getElementById('batchEditModal');

        if (event.target === contentModal) {
            closeContentModal();
        }
        if (event.target === batchEditModal) {
            closeBatchEditModal();
        }
    }
</script>

<!-- 模态框和动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .modal {
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 24px;
        max-height: 50vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 16px 24px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .selected-info {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 16px;
        font-size: 14px;
        color: #666;
    }
</style>

{/block}
