<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'index/hello');

// 用户端API路由
Route::group('api', function () {
    Route::post('/exchange/redeem', 'api.Exchange/redeem');
    Route::get('/exchange/records', 'api.Exchange/records');
    Route::get('/exchange/info', 'api.Exchange/info');
})->allowCrossDomain();

// 卡密管理路由（需要权限验证）
Route::group('cards', function () {
    Route::get('/', 'Card/index');
    Route::get('/generate', 'Card/generate');
    Route::post('/generate', 'Card/generate');
    Route::get('/getCategories', 'Card/getCategories');
    Route::post('/updateStatus', 'Card/updateStatus');
    Route::post('/batchDelete', 'Card/batchDelete');
    Route::post('/export', 'Card/export');
})->middleware(\app\middleware\Auth::class);

// 分类管理路由（需要权限验证）
Route::group('categories', function () {
    Route::get('/', 'Category/index');
    Route::get('/add', 'Category/add');
    Route::post('/add', 'Category/add');
    Route::get('/edit', 'Category/edit');
    Route::post('/edit', 'Category/edit');
    Route::post('/delete', 'Category/delete');
    Route::post('/updateStatus', 'Category/updateStatus');
    Route::post('/updateSort', 'Category/updateSort');
    Route::post('/batchUpdateSort', 'Category/batchUpdateSort');
    Route::get('/getCategories', 'Category/getCategories');
    Route::post('/import', 'Category/import');
    Route::get('/export', 'Category/export');
    Route::get('/insertTestData', 'Category/insertTestData');
})->middleware(\app\middleware\Auth::class);

// 内容管理路由（需要权限验证）
Route::group('content', function () {
    Route::get('/', 'Content/index');
    Route::get('/edit', 'Content/edit');
    Route::post('/edit', 'Content/edit');
    Route::post('/batchEdit', 'Content/batchEdit');
    Route::post('/clearContent', 'Content/clearContent');
    Route::get('/export', 'Content/export');
})->middleware(\app\middleware\Auth::class);

// 系统设置路由（需要权限验证）
Route::group('settings', function () {
    Route::get('/', 'Settings/index');
    Route::post('/', 'Settings/index');
    Route::get('/systemInfo', 'Settings/systemInfo');
    Route::post('/clearCache', 'Settings/clearCache');
    Route::post('/optimizeDatabase', 'Settings/optimizeDatabase');
})->middleware(\app\middleware\Auth::class);

// 生成卡密路由（独立路由，需要权限验证）
Route::group(function () {
    Route::get('/generate', 'Card/generate');
    Route::post('/generate', 'Card/generate');
})->middleware(\app\middleware\Auth::class);

// 用户前端路由
Route::get('/', 'Index/index');
Route::get('/exchange', 'Index/exchange');
Route::post('/exchange', 'Index/exchange');

// 登录相关路由（无需权限验证）
Route::get('/login', 'Admin/login');
Route::post('/login', 'Admin/login');
Route::get('/logout', 'Admin/logout');
Route::get('/create-admin', 'Admin/createDefaultAdmin');

// 管理后台路由（需要权限验证）
Route::group(function () {
    Route::get('/admin', 'Index/admin');
    Route::get('/dashboard', 'Dashboard/index');
    Route::get('/dashboard/chart-data', 'Dashboard/getChartData');
})->middleware(\app\middleware\Auth::class);
