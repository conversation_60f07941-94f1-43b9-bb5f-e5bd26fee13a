<?php
// 临时脚本：检查和创建管理员表及默认账号
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use think\facade\Db;

try {
    echo "开始检查管理员表...\n";
    
    // 检查管理员表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'km_admins'");
    
    if (empty($tables)) {
        echo "❌ 管理员表不存在，正在创建...\n";
        
        // 创建管理员表
        $sql = "
        CREATE TABLE IF NOT EXISTS `km_admins` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
          `username` varchar(50) NOT NULL COMMENT '用户名',
          `password` varchar(255) NOT NULL COMMENT '密码',
          `name` varchar(100) NOT NULL COMMENT '姓名',
          `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
          `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
          `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
          `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
        ";
        
        Db::execute($sql);
        echo "✅ 管理员表创建成功!\n";
    } else {
        echo "✅ 管理员表已存在\n";
    }
    
    // 检查是否已存在默认管理员
    $adminExists = Db::name('admins')->where('username', 'admin')->find();
    
    if (!$adminExists) {
        echo "❌ 默认管理员不存在，正在创建...\n";
        
        // 创建默认管理员
        $adminData = [
            'username' => 'admin',
            'password' => password_hash('admin', PASSWORD_DEFAULT),
            'name' => '系统管理员',
            'email' => '<EMAIL>',
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = Db::name('admins')->insert($adminData);
        
        if ($result) {
            echo "✅ 默认管理员创建成功!\n";
            echo "📋 登录信息:\n";
            echo "   用户名: admin\n";
            echo "   密码: admin\n";
        } else {
            echo "❌ 默认管理员创建失败!\n";
        }
    } else {
        echo "✅ 默认管理员已存在\n";
        echo "📋 管理员信息:\n";
        echo "   ID: {$adminExists['id']}\n";
        echo "   用户名: {$adminExists['username']}\n";
        echo "   姓名: {$adminExists['name']}\n";
        echo "   状态: " . ($adminExists['status'] ? '启用' : '禁用') . "\n";
        echo "   创建时间: {$adminExists['created_at']}\n";
    }
    
    // 验证表结构
    echo "\n📊 验证表结构:\n";
    $columns = Db::query("SHOW COLUMNS FROM km_admins");
    foreach ($columns as $column) {
        echo "   - {$column['Field']}: {$column['Type']}\n";
    }
    
    // 统计管理员数量
    $adminCount = Db::name('admins')->count();
    echo "\n📈 当前管理员总数: {$adminCount}\n";
    
    echo "\n🎉 管理员系统设置完成!\n";
    echo "🔗 登录地址: http://localhost:8000/login\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
    echo "🔍 错误详情: " . $e->getTraceAsString() . "\n";
}
?>
