{extend name="layout/base" /}

{block name="title"}控制台 - 卡密兑换管理系统{/block}

{block name="style"}
<style>
    /* 统计卡片样式 */
    .stats-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 16px 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        margin-bottom: 12px;
        position: relative;
        z-index: 2;
    }
    
    .stats-value {
        font-size: 28px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 6px;
        line-height: 1;
    }

    .stats-label {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 6px;
        font-weight: 500;
    }

    .stats-growth {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
    }
    
    .growth-positive {
        color: var(--success-color);
    }

    .growth-negative {
        color: var(--error-color);
    }

    /* 图标颜色 */
    .icon-primary { background-color: var(--primary-color); }
    .icon-success { background-color: var(--success-color); }
    .icon-warning { background-color: var(--warning-color); }
    .icon-info { background-color: #13c2c2; }
    .icon-purple { background-color: #722ed1; }

    /* 概览容器样式 */
    .overview-container {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .overview-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .overview-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 0;
        padding: 0;
    }

    .stats-grid .stats-card {
        border-radius: 0;
        border: none;
        border-right: 1px solid var(--border-color);
        box-shadow: none;
        margin: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 16px;
        transition: all 0.3s ease;
    }

    .stats-grid .stats-card:last-child {
        border-right: none;
    }

    .stats-grid .stats-card:hover {
        background-color: #f8f9fa;
        transform: none;
        box-shadow: inset 0 0 0 1px var(--primary-color);
    }
    
    /* 图表容器样式 */
    .chart-container {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .chart-controls {
        display: flex;
        gap: 0.5rem;
    }
    
    .chart-controls .btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* 表格样式 */
    .records-table {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #eee;
    }
    
    .table-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    
    .table td {
        border: none;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }
    
    .table tbody tr {
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-used {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-unused {
        background-color: #fff3cd;
        color: #856404;
    }
    
    /* 快速操作样式 */
    .quick-actions {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s ease;
        background: var(--card-bg);
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .action-item:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
        text-decoration: none;
    }
    
    .action-icon {
        font-size: 28px;
        margin-bottom: 12px;
        color: var(--text-secondary);
        transition: color 0.3s ease;
    }

    .action-item:hover .action-icon {
        color: var(--primary-color);
    }

    .action-label {
        font-size: 14px;
        font-weight: 500;
        text-align: center;
    }

    /* 全宽快速操作样式 */
    .quick-actions-full {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .quick-actions-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .quick-actions-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .actions-grid-full {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 0;
        padding: 0;
    }

    .actions-grid-full .action-item {
        border-radius: 0;
        border: none;
        border-right: 1px solid var(--border-color);
        box-shadow: none;
        margin: 0;
        padding: 24px 16px;
        background: var(--card-bg);
    }

    .actions-grid-full .action-item:last-child {
        border-right: none;
    }

    .actions-grid-full .action-item:hover {
        background-color: #f8f9fa;
        transform: none;
        box-shadow: inset 0 0 0 1px var(--primary-color);
    }

    .actions-grid-full .action-item .action-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }
    
    /* 系统状态样式 */
    .system-status-container {
        background: var(--card-bg);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .system-status-header {
        padding: 16px 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--border-color);
    }

    .system-status-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .system-status {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0;
        padding: 0;
    }
    
    .status-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 20px 24px;
        border-right: 1px solid var(--border-color);
        background-color: var(--card-bg);
        transition: all 0.3s ease;
    }

    .status-item:last-child {
        border-right: none;
    }

    .status-item:hover {
        background-color: #f8f9fa;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .status-success {
        background-color: #28a745;
    }
    
    .status-warning {
        background-color: #ffc107;
    }
    
    .status-danger {
        background-color: #dc3545;
    }
    
    .status-text {
        font-size: 0.9rem;
        color: #495057;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .chart-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .stats-grid .stats-card {
            border-right: none;
            border-bottom: 1px solid var(--border-color);
        }

        .stats-grid .stats-card:nth-child(2n) {
            border-right: none;
        }

        .stats-grid .stats-card:last-child,
        .stats-grid .stats-card:nth-last-child(2) {
            border-bottom: none;
        }

        .actions-grid-full {
            grid-template-columns: repeat(3, 1fr);
        }

        .actions-grid-full .action-item {
            border-right: none;
            border-bottom: 1px solid var(--border-color);
        }

        .actions-grid-full .action-item:nth-child(3n) {
            border-right: none;
        }

        .actions-grid-full .action-item:nth-last-child(-n+3) {
            border-bottom: none;
        }

        .system-status {
            grid-template-columns: 1fr;
        }

        .status-item {
            border-right: none;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .actions-grid-full {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{/block}

{block name="content"}
<div class="page-title">
    <h1>控制台</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active">控制台</li>
        </ol>
    </nav>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="overview-container">
            <div class="overview-header">
                <h5 class="overview-title">数据概览</h5>
            </div>
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-icon icon-primary">
                        <i class="{$cardStats.total_cards.icon}"></i>
                    </div>
                    <div class="stats-value">{$cardStats.total_cards.value}</div>
                    <div class="stats-label">总卡密数</div>
                    <div class="stats-growth {$cardStats.total_cards.growth >= 0 ? 'growth-positive' : 'growth-negative'}">
                        <i class="fas fa-arrow-{$cardStats.total_cards.growth >= 0 ? 'up' : 'down'}"></i>
                        {$cardStats.total_cards.growth}%
                    </div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon icon-success">
                        <i class="{$cardStats.used_cards.icon}"></i>
                    </div>
                    <div class="stats-value">{$cardStats.used_cards.value}</div>
                    <div class="stats-label">已使用卡密</div>
                    <div class="stats-growth {$cardStats.used_cards.growth >= 0 ? 'growth-positive' : 'growth-negative'}">
                        <i class="fas fa-arrow-{$cardStats.used_cards.growth >= 0 ? 'up' : 'down'}"></i>
                        {$cardStats.used_cards.growth}%
                    </div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon" style="background-color: var(--error-color);">
                        <i class="{$cardStats.disabled_cards.icon}"></i>
                    </div>
                    <div class="stats-value">{$cardStats.disabled_cards.value}</div>
                    <div class="stats-label">已禁用卡密</div>
                    <div class="stats-growth {$cardStats.disabled_cards.growth >= 0 ? 'growth-positive' : 'growth-negative'}">
                        <i class="fas fa-arrow-{$cardStats.disabled_cards.growth >= 0 ? 'up' : 'down'}"></i>
                        {$cardStats.disabled_cards.growth}%
                    </div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon icon-warning">
                        <i class="{$cardStats.unused_cards.icon}"></i>
                    </div>
                    <div class="stats-value">{$cardStats.unused_cards.value}</div>
                    <div class="stats-label">未使用卡密</div>
                    <div class="stats-growth {$cardStats.unused_cards.growth >= 0 ? 'growth-positive' : 'growth-negative'}">
                        <i class="fas fa-arrow-{$cardStats.unused_cards.growth >= 0 ? 'up' : 'down'}"></i>
                        {$cardStats.unused_cards.growth}%
                    </div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon icon-info">
                        <i class="{$cardStats.category_count.icon}"></i>
                    </div>
                    <div class="stats-value">{$cardStats.category_count.value}</div>
                    <div class="stats-label">分类总数</div>
                    <div class="stats-growth {$cardStats.category_count.growth >= 0 ? 'growth-positive' : 'growth-negative'}">
                        <i class="fas fa-arrow-{$cardStats.category_count.growth >= 0 ? 'up' : 'down'}"></i>
                        {$cardStats.category_count.growth}%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作中心 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="quick-actions-full">
            <div class="quick-actions-header">
                <h5 class="quick-actions-title">快速操作</h5>
            </div>
            <div class="actions-grid-full">
                <a href="/cards/generate" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="action-label">生成卡密</div>
                </a>

                <a href="/cards" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="action-label">卡密管理</div>
                </a>

                <a href="/categories" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="action-label">分类管理</div>
                </a>

                <a href="/content" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="action-label">内容管理</div>
                </a>

                <a href="#" class="action-item" onclick="showExportModal()">
                    <div class="action-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="action-label">导出数据</div>
                </a>

                <a href="/settings" class="action-item">
                    <div class="action-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="action-label">系统设置</div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 使用趋势图表 -->
    <div class="col-lg-8 mb-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="day">日</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="week">周</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="month">月</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="year">年</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageTrendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 分类占比图表 -->
    <div class="col-lg-4 mb-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">分类占比</h5>
            </div>
            <div style="height: 300px;">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近使用记录 -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="records-table">
            <div class="table-header">
                <h5 class="table-title">最近使用记录</h5>
                <a href="/usage-records" class="btn btn-outline-primary btn-sm">查看全部</a>
            </div>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密编号</th>
                            <th>分类</th>
                            <th>使用时间</th>
                            <th>使用IP</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="recentRecords" id="record"}
                        <tr>
                            <td><code>{$record.masked_card_code}</code></td>
                            <td>{$record.category_name}</td>
                            <td>{$record.used_at}</td>
                            <td>{$record.used_ip|default='未知'}</td>
                            <td><span class="status-badge status-used">已使用</span></td>
                        </tr>
                        {/volist}
                        {empty name="recentRecords"}
                        <tr>
                            <td colspan="5" class="text-center text-muted py-4">暂无使用记录</td>
                        </tr>
                        {/empty}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态监控 -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="system-status-container">
            <div class="system-status-header">
                <h5 class="system-status-title">系统状态</h5>
            </div>
            <div class="system-status">
                <div class="status-item">
                    <div class="status-indicator status-{$systemStatus.server_status.color}"></div>
                    <div class="status-text">
                        <strong>服务器状态</strong><br>
                        <small>{$systemStatus.server_status.text}</small>
                    </div>
                </div>

                <div class="status-item">
                    <div class="status-indicator status-{$systemStatus.database_status.color}"></div>
                    <div class="status-text">
                        <strong>数据库连接</strong><br>
                        <small>{$systemStatus.database_status.text}</small>
                    </div>
                </div>

                <div class="status-item">
                    <div class="status-indicator status-{$systemStatus.storage_status.color}"></div>
                    <div class="status-text">
                        <strong>存储空间</strong><br>
                        <small>{$systemStatus.storage_status.text}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportType" class="form-label">导出类型</label>
                        <select class="form-select" id="exportType">
                            <option value="all">全部数据</option>
                            <option value="cards">卡密数据</option>
                            <option value="usage">使用记录</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportData()">导出</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    // 图表实例
    let usageTrendChart = null;
    let categoryChart = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initCharts();
        bindEvents();
    });

    // 初始化图表
    function initCharts() {
        initUsageTrendChart();
        initCategoryChart();
    }

    // 初始化使用趋势图表
    function initUsageTrendChart() {
        const ctx = document.getElementById('usageTrendChart').getContext('2d');

        // 模拟数据，实际应该从后端获取
        const trendData = {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            datasets: [{
                label: '生成卡密',
                data: [120, 190, 300, 500, 200, 300, 450, 320, 280, 350, 400, 380],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '使用卡密',
                data: [80, 150, 250, 400, 180, 280, 380, 290, 250, 320, 360, 340],
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        };

        usageTrendChart = new Chart(ctx, {
            type: 'line',
            data: trendData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '数量'
                        },
                        beginAtZero: true
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    // 初始化分类占比图表
    function initCategoryChart() {
        const ctx = document.getElementById('categoryChart').getContext('2d');

        // 从后端数据构建图表数据
        const categoryData = {
            labels: {$categoryStats|json_encode|raw},
            datasets: [{
                data: {$categoryStats|json_encode|raw},
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        // 处理数据格式
        if (categoryData.labels && categoryData.labels.length > 0) {
            categoryData.labels = categoryData.labels.map(item => item.name);
            categoryData.datasets[0].data = categoryData.datasets[0].data.map(item => item.value);
        } else {
            categoryData.labels = ['暂无数据'];
            categoryData.datasets[0].data = [1];
            categoryData.datasets[0].backgroundColor = ['#e9ecef'];
        }

        categoryChart = new Chart(ctx, {
            type: 'doughnut',
            data: categoryData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    // 绑定事件
    function bindEvents() {
        // 时间维度切换
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // 更新图表数据
                const period = this.dataset.period;
                updateUsageTrendChart(period);
            });
        });
    }

    // 更新使用趋势图表
    function updateUsageTrendChart(period) {
        // 这里应该发送AJAX请求获取新数据
        // 为了演示，我们使用模拟数据
        let newData = {};

        switch(period) {
            case 'day':
                newData = {
                    labels: Array.from({length: 30}, (_, i) => `${i+1}日`),
                    created: Array.from({length: 30}, () => Math.floor(Math.random() * 50) + 10),
                    used: Array.from({length: 30}, () => Math.floor(Math.random() * 40) + 5)
                };
                break;
            case 'week':
                newData = {
                    labels: Array.from({length: 12}, (_, i) => `第${i+1}周`),
                    created: Array.from({length: 12}, () => Math.floor(Math.random() * 200) + 50),
                    used: Array.from({length: 12}, () => Math.floor(Math.random() * 150) + 30)
                };
                break;
            case 'month':
                newData = {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    created: [120, 190, 300, 500, 200, 300, 450, 320, 280, 350, 400, 380],
                    used: [80, 150, 250, 400, 180, 280, 380, 290, 250, 320, 360, 340]
                };
                break;
            case 'year':
                newData = {
                    labels: ['2020', '2021', '2022', '2023', '2024'],
                    created: [1200, 1800, 2500, 3200, 2800],
                    used: [1000, 1500, 2100, 2800, 2400]
                };
                break;
        }

        usageTrendChart.data.labels = newData.labels;
        usageTrendChart.data.datasets[0].data = newData.created;
        usageTrendChart.data.datasets[1].data = newData.used;
        usageTrendChart.update();
    }

    // 显示导出模态框
    function showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }

    // 导出数据
    function exportData() {
        const password = document.getElementById('exportPassword').value;
        const type = document.getElementById('exportType').value;

        if (!password) {
            alert('请输入导出密码');
            return;
        }

        // 这里应该发送AJAX请求到后端
        // 为了演示，我们只是显示一个提示
        alert(`正在导出${type === 'all' ? '全部' : type === 'cards' ? '卡密' : '使用记录'}数据...`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();

        // 清空表单
        document.getElementById('exportForm').reset();
    }
</script>
