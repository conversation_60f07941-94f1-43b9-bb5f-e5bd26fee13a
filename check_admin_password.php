<?php
require_once 'vendor/autoload.php';

$app = new think\App();
$app->initialize();

use think\facade\Db;

try {
    echo "检查管理员账户信息...\n";
    
    $admin = Db::name('km_admin_users')->where('username', 'admin')->find();
    
    if ($admin) {
        echo "✅ 找到管理员账户:\n";
        echo "   ID: {$admin['id']}\n";
        echo "   用户名: {$admin['username']}\n";
        echo "   邮箱: {$admin['email']}\n";
        echo "   状态: " . ($admin['status'] ? '启用' : '禁用') . "\n";
        echo "   密码哈希: {$admin['password']}\n";
        
        // 测试密码验证
        echo "\n🔍 测试密码验证:\n";
        $testPasswords = ['admin', 'admin123', '123456'];
        
        foreach ($testPasswords as $testPassword) {
            $isValid = password_verify($testPassword, $admin['password']);
            echo "   密码 '{$testPassword}': " . ($isValid ? '✅ 正确' : '❌ 错误') . "\n";
        }
        
    } else {
        echo "❌ 未找到用户名为 'admin' 的管理员账户\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
?>
