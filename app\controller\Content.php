<?php

namespace app\controller;

use app\BaseController;
use app\model\Card;
use app\model\Category;
use think\facade\Db;

/**
 * 内容管理控制器
 */
class Content extends BaseController
{
    /**
     * 内容管理首页
     */
    public function index()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $status = $this->request->param('status', '');
        $has_content = $this->request->param('has_content', '');
        $keyword = $this->request->param('keyword', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['c.category_id', '=', $category_id];
        }
        if ($status !== '') {
            $where[] = ['c.status', '=', $status];
        }
        if ($has_content !== '') {
            if ($has_content == '1') {
                $where[] = ['c.content', '<>', ''];
            } else {
                $where[] = ['c.content', '=', ''];
            }
        }
        if ($keyword) {
            $where[] = ['c.card_code|c.content', 'like', '%' . $keyword . '%'];
        }

        // 查询卡密内容列表
        $contents = Card::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.id',
                'c.card_code',
                'c.category_id',
                'c.status',
                'c.content',
                'c.sort_order',
                'c.used_at',
                'c.expire_at',
                'c.created_at',
                'c.updated_at',
                'cat.name as category_name'
            ])
            ->where($where)
            ->order('c.sort_order', 'desc')
            ->order('c.updated_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 获取分类列表（层级结构）
        $categories = Category::getFlatCategories();

        // 获取统计数据
        $stats = $this->getContentStats();

        return view('content/index', [
            'contents' => $contents,
            'categories' => $categories,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'category_id' => $category_id,
                'has_content' => $has_content,
                'keyword' => $keyword
            ]
        ]);
    }

    /**
     * 编辑内容页面
     */
    public function edit()
    {
        $id = $this->request->param('id');

        if ($this->request->isPost()) {
            return $this->doEdit();
        }

        $card = Card::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.*',
                'cat.name as category_name'
            ])
            ->where('c.id', $id)
            ->find();

        if (!$card) {
            $this->error('卡密不存在');
        }

        $categories = Category::getEnabledCategories();

        return view('content/edit', [
            'card' => $card,
            'categories' => $categories
        ]);
    }

    /**
     * 执行编辑内容
     */
    private function doEdit()
    {
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'id' => 'require|integer',
            'content' => 'require|max:5000',
            'expire_at' => 'date'
        ]);

        if ($validate !== true) {
            return json(['code' => 400, 'message' => $validate]);
        }

        try {
            $card = Card::find($data['id']);
            if (!$card) {
                return json(['code' => 404, 'message' => '卡密不存在']);
            }

            // 更新内容
            $updateData = [
                'content' => $data['content']
            ];

            if (!empty($data['expire_at'])) {
                $updateData['expire_at'] = $data['expire_at'];
            }

            $card->save($updateData);

            return json(['code' => 200, 'message' => '内容更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量编辑内容
     */
    public function batchEdit()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();
        
        if (empty($data['ids']) || empty($data['content'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        try {
            $ids = explode(',', $data['ids']);
            $updateData = ['content' => $data['content']];
            
            if (!empty($data['expire_at'])) {
                $updateData['expire_at'] = $data['expire_at'];
            }

            $count = Card::whereIn('id', $ids)->update($updateData);

            return json(['code' => 200, 'message' => "成功更新 {$count} 个卡密的内容"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '批量更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 清空内容
     */
    public function clearContent()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();
        
        if (empty($data['ids'])) {
            return json(['code' => 400, 'message' => '请选择要清空的卡密']);
        }

        try {
            $ids = explode(',', $data['ids']);
            $count = Card::whereIn('id', $ids)->update(['content' => '']);

            return json(['code' => 200, 'message' => "成功清空 {$count} 个卡密的内容"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '清空失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取内容统计数据
     */
    private function getContentStats()
    {
        $totalCards = Card::count();
        $hasContent = Card::where('content', '<>', '')->count();
        $noContent = $totalCards - $hasContent;
        $expiredCards = Card::where('expire_at', '<', date('Y-m-d H:i:s'))->count();

        return [
            'total' => $totalCards,
            'has_content' => $hasContent,
            'no_content' => $noContent,
            'expired' => $expiredCards
        ];
    }

    /**
     * 导出内容
     */
    public function export()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $status = $this->request->param('status', '');
        $keyword = $this->request->param('keyword', '');

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['c.category_id', '=', $category_id];
        }
        if ($status !== '') {
            $where[] = ['c.status', '=', $status];
        }
        if ($keyword) {
            $where[] = ['c.card_code|c.content', 'like', '%' . $keyword . '%'];
        }

        // 查询数据
        $contents = Card::alias('c')
            ->leftJoin('km_categories cat', 'c.category_id = cat.id')
            ->field([
                'c.card_code',
                'cat.name as category_name',
                'c.status',
                'c.content',
                'c.created_at',
                'c.updated_at',
                'c.expire_at'
            ])
            ->where($where)
            ->order('c.updated_at', 'desc')
            ->select();

        // 生成CSV内容
        $csv = "卡密编号,分类,状态,内容,创建时间,更新时间,过期时间\n";
        foreach ($contents as $content) {
            $statusText = match($content['status']) {
                Card::STATUS_UNUSED => '未使用',
                Card::STATUS_USED => '已使用',
                Card::STATUS_DISABLED => '已禁用',
                default => '未知'
            };

            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s\n",
                $content['card_code'],
                $content['category_name'],
                $statusText,
                str_replace(["\r", "\n", ","], ["", "", "，"], $content['content']),
                $content['created_at'],
                $content['updated_at'],
                $content['expire_at'] ?: ''
            );
        }

        // 设置响应头
        $filename = 'content_export_' . date('Y-m-d_H-i-s') . '.csv';
        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * 更新排序
     */
    public function updateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();

        if (empty($data['id']) || !isset($data['sort_order'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        $sortOrder = intval($data['sort_order']);
        if ($sortOrder < 0 || $sortOrder > 9999) {
            return json(['code' => 400, 'message' => '排序值必须在0-9999之间']);
        }

        try {
            $card = Card::find($data['id']);
            if (!$card) {
                return json(['code' => 404, 'message' => '卡密不存在']);
            }

            $card->sort_order = $sortOrder;
            $card->save();

            return json(['code' => 200, 'message' => '排序更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '排序更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量更新排序
     */
    public function batchUpdateSort()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();

        if (empty($data['ids']) || !isset($data['start_sort'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        $startSort = intval($data['start_sort']);
        if ($startSort < 0 || $startSort > 9999) {
            return json(['code' => 400, 'message' => '起始排序值必须在0-9999之间']);
        }

        try {
            $ids = explode(',', $data['ids']);
            $currentSort = $startSort;

            foreach ($ids as $id) {
                Card::where('id', $id)->update(['sort_order' => $currentSort]);
                $currentSort++;
            }

            $count = count($ids);
            return json(['code' => 200, 'message' => "成功更新 {$count} 个内容的排序"]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '批量排序更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 复制内容
     */
    public function duplicate()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $data = $this->request->post();

        if (empty($data['id'])) {
            return json(['code' => 400, 'message' => '参数不完整']);
        }

        try {
            $sourceCard = Card::find($data['id']);
            if (!$sourceCard) {
                return json(['code' => 404, 'message' => '源卡密不存在']);
            }

            // 查找一个未使用且无内容的卡密
            $targetCard = Card::where('status', Card::STATUS_UNUSED)
                ->where('content', '')
                ->find();

            if (!$targetCard) {
                return json(['code' => 400, 'message' => '没有可用的空白卡密进行复制']);
            }

            // 复制内容和相关属性
            $targetCard->content = $sourceCard->content;
            $targetCard->sort_order = $sourceCard->sort_order;
            $targetCard->expire_at = $sourceCard->expire_at;
            $targetCard->save();

            return json(['code' => 200, 'message' => '内容复制成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '内容复制失败：' . $e->getMessage()]);
        }
    }
}
