<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { 
            width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
        }
        button { 
            background: #007bff; color: white; padding: 10px 20px; 
            border: none; border-radius: 4px; cursor: pointer; 
        }
        button:hover { background: #0056b3; }
        .alert { 
            padding: 10px; margin: 10px 0; border-radius: 4px; 
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .debug-info { 
            background: #f8f9fa; padding: 15px; margin: 20px 0; 
            border-radius: 4px; border: 1px solid #dee2e6; 
        }
        .debug-info h3 { margin-top: 0; }
        .debug-info pre { background: white; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录测试页面</h1>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="admin" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <div class="debug-info">
            <h3>调试信息</h3>
            <div id="debugInfo">
                <p>等待登录请求...</p>
            </div>
        </div>
        
        <div class="debug-info">
            <h3>快速测试链接</h3>
            <p><a href="/dashboard" target="_blank">直接访问Dashboard</a></p>
            <p><a href="/cards" target="_blank">直接访问卡密管理</a></p>
            <p><a href="/login" target="_blank">访问原登录页面</a></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const btn = document.getElementById('loginBtn');
            const alertContainer = document.getElementById('alertContainer');
            const debugInfo = document.getElementById('debugInfo');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                
                if (!username || !password) {
                    showAlert('请输入用户名和密码', 'danger');
                    return;
                }
                
                // 显示调试信息
                updateDebugInfo('发送登录请求...');
                
                // 发送登录请求
                fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                })
                .then(response => {
                    updateDebugInfo(`响应状态: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    updateDebugInfo(`响应数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.code === 200) {
                        showAlert(data.message, 'success');
                        updateDebugInfo(`登录成功，准备重定向到: ${data.redirect}`);
                        
                        // 延迟重定向，让用户看到调试信息
                        setTimeout(() => {
                            updateDebugInfo(`正在重定向到: ${data.redirect}`);
                            window.location.href = data.redirect || '/dashboard';
                        }, 2000);
                    } else {
                        showAlert(data.message, 'danger');
                        updateDebugInfo(`登录失败: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('登录错误:', error);
                    showAlert('网络错误，请稍后重试', 'danger');
                    updateDebugInfo(`网络错误: ${error.message}`);
                });
            });
            
            function showAlert(message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
            }
            
            function updateDebugInfo(info) {
                const timestamp = new Date().toLocaleTimeString();
                debugInfo.innerHTML += `<p>[${timestamp}] ${info}</p>`;
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }
        });
    </script>
</body>
</html>
