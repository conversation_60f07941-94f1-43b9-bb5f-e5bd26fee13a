-- 卡密兑换系统数据库表结构
-- 创建时间: 2025-07-31

-- 1. 卡密分类表
CREATE TABLE `km_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父分类ID，0为顶级分类',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `level` tinyint(2) NOT NULL DEFAULT '1' COMMENT '分类层级，1为顶级',
  `path` varchar(500) NOT NULL DEFAULT '' COMMENT '分类路径，如：1,2,3',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_level` (`level`),
  KEY `idx_path` (`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密分类表';

-- 2. 卡密主表
CREATE TABLE `km_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卡密ID',
  `card_code` varchar(50) NOT NULL COMMENT '卡密编号',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0未使用，1已使用，2已禁用',
  `content` text COMMENT '卡密内容/兑换内容',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `used_ip` varchar(45) DEFAULT NULL COMMENT '使用IP地址',
  `expire_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_code` (`card_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_used_at` (`used_at`),
  CONSTRAINT `fk_cards_category` FOREIGN KEY (`category_id`) REFERENCES `km_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密主表';

-- 3. 卡密使用记录表
CREATE TABLE `km_usage_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `card_id` int(11) NOT NULL COMMENT '卡密ID',
  `card_code` varchar(50) NOT NULL COMMENT '卡密编号',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `used_ip` varchar(45) DEFAULT NULL COMMENT '使用IP地址',
  `user_agent` text COMMENT '用户代理',
  `used_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_used_at` (`used_at`),
  CONSTRAINT `fk_usage_records_card` FOREIGN KEY (`card_id`) REFERENCES `km_cards` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_usage_records_category` FOREIGN KEY (`category_id`) REFERENCES `km_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密使用记录表';

-- 4. 系统设置表
CREATE TABLE `km_system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `setting_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '设置类型：string,int,bool,json',
  `description` varchar(255) DEFAULT NULL COMMENT '设置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 5. 管理员用户表
CREATE TABLE `km_admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 6. 操作日志表
CREATE TABLE `km_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) NOT NULL COMMENT '操作描述',
  `request_data` text COMMENT '请求数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_operation_logs_admin` FOREIGN KEY (`admin_id`) REFERENCES `km_admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 插入默认数据
-- 插入默认分类
INSERT INTO `km_categories` (`name`, `description`, `sort_order`) VALUES
('高级会员', '高级会员卡密', 1),
('年度会员', '年度会员卡密', 2),
('终身会员', '终身会员卡密', 3);

-- 插入默认系统设置
INSERT INTO `km_system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('export_password', '', 'string', '数据导出密码'),
('site_title', '卡密兑换管理系统', 'string', '网站标题'),
('cards_per_page', '20', 'int', '每页显示卡密数量'),
('enable_ip_limit', '0', 'bool', '是否启用IP限制'),
('max_daily_usage', '10', 'int', '每日最大使用次数');

-- 注意：此文件已废弃，请使用统一的 admins 表
-- 默认管理员账户请通过 setup_admin.php 脚本创建
