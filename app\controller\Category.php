<?php

namespace app\controller;

use app\BaseController;
use app\model\Category as CategoryModel;
use think\facade\Db;

/**
 * 分类管理控制器
 */
class Category extends BaseController
{
    /**
     * 分类列表页面
     */
    public function index()
    {
        // 获取分类树形结构
        $categoryTree = CategoryModel::getCategoryTree();

        // 获取扁平化分类列表（用于表格显示）
        $categories = CategoryModel::getFlatCategories();

        return view('category/index', [
            'categories' => $categories,
            'categoryTree' => $categoryTree
        ]);
    }
    
    /**
     * 添加分类页面
     */
    public function add()
    {
        if ($this->request->isPost()) {
            return $this->doAdd();
        }
        
        // 获取可选的父分类
        $parentCategories = CategoryModel::getFlatCategories();
        
        return view('category/add', [
            'parentCategories' => $parentCategories
        ]);
    }
    
    /**
     * 执行添加分类
     */
    private function doAdd()
    {
        $data = $this->request->post();
        
        // 验证参数
        if (empty($data['name'])) {
            return json(['code' => 400, 'message' => '分类名称不能为空']);
        }
        
        $parentId = (int)($data['parent_id'] ?? 0);
        $name = trim($data['name']);
        $description = trim($data['description'] ?? '');
        $sortOrder = (int)($data['sort_order'] ?? 0);
        
        // 检查同级分类名称是否重复
        $exists = CategoryModel::where('parent_id', $parentId)
            ->where('name', $name)
            ->find();
        
        if ($exists) {
            return json(['code' => 400, 'message' => '同级分类中已存在相同名称']);
        }
        
        // 检查父分类是否存在且启用
        if ($parentId > 0) {
            $parent = CategoryModel::find($parentId);
            if (!$parent || $parent->status != CategoryModel::STATUS_ENABLED) {
                return json(['code' => 400, 'message' => '父分类不存在或已禁用']);
            }
            
            // 检查层级深度（最多3级）
            if ($parent->level >= 3) {
                return json(['code' => 400, 'message' => '分类层级不能超过3级']);
            }
        }
        
        Db::startTrans();
        try {
            // 创建分类
            $category = new CategoryModel();
            $category->parent_id = $parentId;
            $category->name = $name;
            $category->description = $description;
            $category->sort_order = $sortOrder;
            $category->status = CategoryModel::STATUS_ENABLED;
            $category->save();
            
            // 更新路径信息
            $category->updatePath();
            
            Db::commit();
            return json(['code' => 200, 'message' => '分类添加成功']);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '添加失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 编辑分类页面
     */
    public function edit()
    {
        $id = $this->request->param('id');
        
        if ($this->request->isPost()) {
            return $this->doEdit($id);
        }
        
        $category = CategoryModel::find($id);
        if (!$category) {
            $this->error('分类不存在');
        }
        
        // 获取可选的父分类（排除自己和子分类）
        $excludeIds = array_merge([$id], $category->getAllChildrenIds());
        $parentCategories = CategoryModel::whereNotIn('id', $excludeIds)
            ->order('sort_order', 'asc')
            ->select();
        
        $flatParents = [];
        foreach ($parentCategories as $parent) {
            $flatParents[] = [
                'id' => $parent->id,
                'name' => $parent->getFullPath(),
                'level' => $parent->level
            ];
        }
        
        return view('category/edit', [
            'category' => $category,
            'parentCategories' => $flatParents
        ]);
    }
    
    /**
     * 执行编辑分类
     */
    private function doEdit($id)
    {
        $category = CategoryModel::find($id);
        if (!$category) {
            return json(['code' => 404, 'message' => '分类不存在']);
        }
        
        $data = $this->request->post();
        
        // 验证参数
        if (empty($data['name'])) {
            return json(['code' => 400, 'message' => '分类名称不能为空']);
        }
        
        $parentId = (int)($data['parent_id'] ?? 0);
        $name = trim($data['name']);
        $description = trim($data['description'] ?? '');
        $sortOrder = (int)($data['sort_order'] ?? 0);
        
        // 检查是否设置自己为父分类
        if ($parentId == $id) {
            return json(['code' => 400, 'message' => '不能设置自己为父分类']);
        }
        
        // 检查是否设置子分类为父分类
        $childrenIds = $category->getAllChildrenIds();
        if (in_array($parentId, $childrenIds)) {
            return json(['code' => 400, 'message' => '不能设置子分类为父分类']);
        }
        
        // 检查同级分类名称是否重复
        $exists = CategoryModel::where('parent_id', $parentId)
            ->where('name', $name)
            ->where('id', '<>', $id)
            ->find();
        
        if ($exists) {
            return json(['code' => 400, 'message' => '同级分类中已存在相同名称']);
        }
        
        Db::startTrans();
        try {
            $category->parent_id = $parentId;
            $category->name = $name;
            $category->description = $description;
            $category->sort_order = $sortOrder;
            $category->save();
            
            // 如果父分类发生变化，更新路径信息
            if ($category->parent_id != $parentId) {
                $category->updatePath();
            }
            
            Db::commit();
            return json(['code' => 200, 'message' => '分类更新成功']);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除分类
     */
    public function delete()
    {
        $id = $this->request->post('id');
        
        $category = CategoryModel::find($id);
        if (!$category) {
            return json(['code' => 404, 'message' => '分类不存在']);
        }
        
        if (!$category->canDelete()) {
            return json(['code' => 400, 'message' => '该分类下还有子分类或卡密，无法删除']);
        }
        
        if ($category->delete()) {
            return json(['code' => 200, 'message' => '删除成功']);
        } else {
            return json(['code' => 500, 'message' => '删除失败']);
        }
    }
    
    /**
     * 更新分类状态
     */
    public function updateStatus()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');

        if (!in_array($status, [CategoryModel::STATUS_ENABLED, CategoryModel::STATUS_DISABLED])) {
            return json(['code' => 400, 'message' => '状态参数错误']);
        }

        $category = CategoryModel::find($id);
        if (!$category) {
            return json(['code' => 404, 'message' => '分类不存在']);
        }

        // 如果要禁用分类，检查是否有子分类或卡密
        if ($status == CategoryModel::STATUS_DISABLED) {
            $childCount = CategoryModel::where('parent_id', $id)->count();
            if ($childCount > 0) {
                return json(['code' => 400, 'message' => '该分类下还有子分类，无法禁用']);
            }

            $cardCount = \app\model\Card::where('category_id', $id)->count();
            if ($cardCount > 0) {
                return json(['code' => 400, 'message' => '该分类下还有卡密，无法禁用']);
            }
        }

        $category->status = $status;
        if ($category->save()) {
            return json(['code' => 200, 'message' => '状态更新成功']);
        } else {
            return json(['code' => 500, 'message' => '状态更新失败']);
        }
    }

    /**
     * 更新分类排序
     */
    public function updateSort()
    {
        $id = $this->request->post('id');
        $sortOrder = $this->request->post('sort_order');

        if (!$id || !is_numeric($sortOrder)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }

        $category = CategoryModel::find($id);
        if (!$category) {
            return json(['code' => 404, 'message' => '分类不存在']);
        }

        Db::startTrans();
        try {
            $oldSort = $category->sort_order;
            $newSort = (int)$sortOrder;
            $parentId = $category->parent_id;

            // 如果排序值增加，将中间的分类排序值减1
            if ($newSort > $oldSort) {
                CategoryModel::where('parent_id', $parentId)
                    ->where('sort_order', '>', $oldSort)
                    ->where('sort_order', '<=', $newSort)
                    ->where('id', '<>', $id)
                    ->dec('sort_order', 1);
            }
            // 如果排序值减少，将中间的分类排序值加1
            else if ($newSort < $oldSort) {
                CategoryModel::where('parent_id', $parentId)
                    ->where('sort_order', '>=', $newSort)
                    ->where('sort_order', '<', $oldSort)
                    ->where('id', '<>', $id)
                    ->inc('sort_order', 1);
            }

            // 更新当前分类的排序值
            $category->sort_order = $newSort;
            $category->save();

            Db::commit();
            return json(['code' => 200, 'message' => '排序更新成功']);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '排序更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量更新分类排序
     */
    public function batchUpdateSort()
    {
        $updates = $this->request->post('updates');

        if (!$updates || !is_array($updates)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }

        Db::startTrans();
        try {
            $successCount = 0;
            $errorMessages = [];

            foreach ($updates as $update) {
                if (!isset($update['id']) || !isset($update['sort_order'])) {
                    $errorMessages[] = '缺少必要参数';
                    continue;
                }

                $category = CategoryModel::find($update['id']);
                if (!$category) {
                    $errorMessages[] = "分类ID {$update['id']} 不存在";
                    continue;
                }

                $category->sort_order = (int)$update['sort_order'];
                if ($category->save()) {
                    $successCount++;
                } else {
                    $errorMessages[] = "分类ID {$update['id']} 更新失败";
                }
            }

            if ($successCount > 0 && empty($errorMessages)) {
                Db::commit();
                return json(['code' => 200, 'message' => "成功更新 {$successCount} 个分类的排序"]);
            } else if ($successCount > 0) {
                Db::commit();
                return json(['code' => 200, 'message' => "成功更新 {$successCount} 个分类，部分失败：" . implode(', ', $errorMessages)]);
            } else {
                Db::rollback();
                return json(['code' => 500, 'message' => '批量更新失败：' . implode(', ', $errorMessages)]);
            }

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'message' => '批量更新失败：' . $e->getMessage()]);
        }
    }


    
    /**
     * 获取分类的JSON数据（用于AJAX）
     */
    public function getCategories()
    {
        $parentId = $this->request->param('parent_id', 0);

        $categories = CategoryModel::where('parent_id', $parentId)
            ->where('status', CategoryModel::STATUS_ENABLED)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();

        return json(['code' => 200, 'data' => $categories]);
    }

    /**
     * 导入分类
     */
    public function import()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'message' => '请求方法不允许']);
        }

        $file = $this->request->file('file');
        if (!$file) {
            return json(['code' => 400, 'message' => '请选择要导入的文件']);
        }

        try {
            $extension = strtolower($file->getOriginalExtension());
            $content = '';

            switch ($extension) {
                case 'json':
                    $content = file_get_contents($file->getPathname());
                    $data = json_decode($content, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return json(['code' => 400, 'message' => 'JSON文件格式错误']);
                    }
                    break;

                case 'csv':
                    $data = $this->parseCsvFile($file->getPathname());
                    break;

                case 'xlsx':
                case 'xls':
                    return json(['code' => 400, 'message' => 'Excel文件导入功能开发中']);

                default:
                    return json(['code' => 400, 'message' => '不支持的文件格式，请使用JSON或CSV文件']);
            }

            if (empty($data)) {
                return json(['code' => 400, 'message' => '文件内容为空或格式错误']);
            }

            $result = $this->importCategoryData($data);
            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '导入失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出分类
     */
    public function export()
    {
        $format = $this->request->param('format', 'json');

        try {
            $categories = CategoryModel::order('sort_order', 'asc')
                ->order('id', 'asc')
                ->select()
                ->toArray();

            $filename = 'categories_' . date('Y-m-d_H-i-s');

            switch ($format) {
                case 'json':
                    return $this->exportAsJson($categories, $filename);

                case 'csv':
                    return $this->exportAsCsv($categories, $filename);

                case 'excel':
                    return json(['code' => 400, 'message' => 'Excel导出功能开发中']);

                default:
                    return json(['code' => 400, 'message' => '不支持的导出格式']);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '导出失败：' . $e->getMessage()]);
        }
    }

    /**
     * 插入测试数据
     */
    public function insertTestData()
    {
        try {
            // 清空现有数据
            CategoryModel::where('1=1')->delete();

            // 插入一级分类
            $categories = [
                ['id' => 1, 'parent_id' => 0, 'name' => '会员卡密', 'description' => '各种会员等级的卡密分类', 'status' => 1, 'sort_order' => 10, 'level' => 1, 'path' => '1'],
                ['id' => 2, 'parent_id' => 0, 'name' => '软件授权', 'description' => '软件产品授权卡密', 'status' => 1, 'sort_order' => 20, 'level' => 1, 'path' => '2'],
                ['id' => 3, 'parent_id' => 0, 'name' => '游戏道具', 'description' => '游戏相关道具和装备', 'status' => 1, 'sort_order' => 30, 'level' => 1, 'path' => '3'],
                ['id' => 4, 'parent_id' => 0, 'name' => '教育资源', 'description' => '教育培训相关资源', 'status' => 1, 'sort_order' => 40, 'level' => 1, 'path' => '4'],
                ['id' => 5, 'parent_id' => 0, 'name' => '数字内容', 'description' => '数字化内容产品', 'status' => 1, 'sort_order' => 50, 'level' => 1, 'path' => '5'],
            ];

            // 插入二级分类
            $subCategories = [
                // 会员卡密的子分类
                ['id' => 11, 'parent_id' => 1, 'name' => '基础会员', 'description' => '基础会员权限，30天有效期', 'status' => 1, 'sort_order' => 10, 'level' => 2, 'path' => '1,11'],
                ['id' => 12, 'parent_id' => 1, 'name' => '高级会员', 'description' => '高级会员权限，90天有效期', 'status' => 1, 'sort_order' => 20, 'level' => 2, 'path' => '1,12'],
                ['id' => 13, 'parent_id' => 1, 'name' => '年度会员', 'description' => '年度会员权限，365天有效期', 'status' => 1, 'sort_order' => 30, 'level' => 2, 'path' => '1,13'],
                ['id' => 14, 'parent_id' => 1, 'name' => '终身会员', 'description' => '终身会员权限，永久有效', 'status' => 1, 'sort_order' => 40, 'level' => 2, 'path' => '1,14'],

                // 软件授权的子分类
                ['id' => 21, 'parent_id' => 2, 'name' => 'Office套件', 'description' => 'Microsoft Office等办公软件', 'status' => 1, 'sort_order' => 10, 'level' => 2, 'path' => '2,21'],
                ['id' => 22, 'parent_id' => 2, 'name' => '设计软件', 'description' => 'Photoshop、AI等设计软件', 'status' => 1, 'sort_order' => 20, 'level' => 2, 'path' => '2,22'],
                ['id' => 23, 'parent_id' => 2, 'name' => '开发工具', 'description' => '编程开发相关工具软件', 'status' => 1, 'sort_order' => 30, 'level' => 2, 'path' => '2,23'],

                // 游戏道具的子分类
                ['id' => 31, 'parent_id' => 3, 'name' => '装备道具', 'description' => '游戏装备和道具', 'status' => 1, 'sort_order' => 10, 'level' => 2, 'path' => '3,31'],
                ['id' => 32, 'parent_id' => 3, 'name' => '游戏币', 'description' => '各种游戏内货币', 'status' => 1, 'sort_order' => 20, 'level' => 2, 'path' => '3,32'],
            ];

            // 合并所有分类数据
            $allCategories = array_merge($categories, $subCategories);

            // 批量插入
            foreach ($allCategories as $category) {
                CategoryModel::create($category);
            }

            return json(['code' => 200, 'message' => '测试数据插入成功！']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '插入失败：' . $e->getMessage()]);
        }
    }

    /**
     * 解析CSV文件
     */
    private function parseCsvFile($filepath)
    {
        $data = [];
        $handle = fopen($filepath, 'r');

        if ($handle !== false) {
            $headers = fgetcsv($handle); // 读取表头

            while (($row = fgetcsv($handle)) !== false) {
                if (count($row) >= count($headers)) {
                    $item = array_combine($headers, $row);
                    $data[] = $item;
                }
            }

            fclose($handle);
        }

        return $data;
    }

    /**
     * 导入分类数据
     */
    private function importCategoryData($data)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        Db::startTrans();
        try {
            foreach ($data as $index => $item) {
                try {
                    // 验证必要字段
                    if (empty($item['name'])) {
                        $errors[] = "第" . ($index + 1) . "行：分类名称不能为空";
                        $errorCount++;
                        continue;
                    }

                    // 检查分类是否已存在
                    $parentId = isset($item['parent_id']) ? (int)$item['parent_id'] : 0;
                    $exists = CategoryModel::where('parent_id', $parentId)
                        ->where('name', $item['name'])
                        ->find();

                    if ($exists) {
                        $errors[] = "第" . ($index + 1) . "行：分类「{$item['name']}」已存在";
                        $errorCount++;
                        continue;
                    }

                    // 创建分类
                    $category = new CategoryModel();
                    $category->parent_id = $parentId;
                    $category->name = $item['name'];
                    $category->description = $item['description'] ?? '';
                    $category->sort_order = isset($item['sort_order']) ? (int)$item['sort_order'] : 0;
                    $category->status = isset($item['status']) ? (int)$item['status'] : CategoryModel::STATUS_ENABLED;
                    $category->save();

                    // 更新路径信息
                    $category->updatePath();

                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = "第" . ($index + 1) . "行：" . $e->getMessage();
                    $errorCount++;
                }
            }

            Db::commit();

            $message = "导入完成！成功：{$successCount}条，失败：{$errorCount}条";
            if (!empty($errors)) {
                $message .= "\n\n错误详情：\n" . implode("\n", array_slice($errors, 0, 10));
                if (count($errors) > 10) {
                    $message .= "\n...还有" . (count($errors) - 10) . "个错误";
                }
            }

            return [
                'code' => 200,
                'message' => $message,
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ]
            ];

        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 500, 'message' => '导入失败：' . $e->getMessage()];
        }
    }

    /**
     * 导出为JSON格式
     */
    private function exportAsJson($categories, $filename)
    {
        $json = json_encode($categories, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        return response($json)
            ->header('Content-Type', 'application/json')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '.json"');
    }

    /**
     * 导出为CSV格式
     */
    private function exportAsCsv($categories, $filename)
    {
        $csv = "ID,父分类ID,分类名称,描述,排序,状态,层级,路径,创建时间,更新时间\n";

        foreach ($categories as $category) {
            $csv .= sprintf(
                "%d,%d,\"%s\",\"%s\",%d,%d,%d,\"%s\",\"%s\",\"%s\"\n",
                $category['id'],
                $category['parent_id'],
                str_replace('"', '""', $category['name']),
                str_replace('"', '""', $category['description']),
                $category['sort_order'],
                $category['status'],
                $category['level'],
                $category['path'],
                $category['created_at'],
                $category['updated_at']
            );
        }

        return response($csv)
            ->header('Content-Type', 'text/csv; charset=utf-8')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '.csv"');
    }
}
