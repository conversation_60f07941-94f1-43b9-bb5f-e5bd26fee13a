<?php /*a:2:{s:44:"F:\linshi\thphp\kmxt\view\account\index.html";i:**********;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户设置 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --sidebar-bg: #001529;
            --sidebar-text: rgba(255, 255, 255, 0.65);
            --sidebar-active: #1890ff;
            --content-bg: #f0f2f5;
            --card-bg: #ffffff;
            --border-color: #d9d9d9;
            --text-primary: #262626;
            --text-secondary: #8c8c8c;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
            background-color: var(--content-bg);
            padding: 24px;
        }

        .main-content.expanded {
            margin-left: 0;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 24px;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    /* 设置容器 */
    .settings-container {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 24px;
        max-width: 1200px;
    }

    /* 设置侧边栏 */
    .settings-sidebar {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        height: fit-content;
    }

    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .nav-item {
        margin-bottom: 8px;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
    }

    .nav-link:hover {
        background-color: #f8f9fa;
        color: var(--text-primary);
        text-decoration: none;
    }

    .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }

    .nav-link i {
        width: 16px;
        text-align: center;
    }

    /* 主内容区 */
    .settings-main {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .main-header {
        padding: 24px;
        border-bottom: 1px solid #f0f0f0;
        background: #f8f9fa;
    }

    .main-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .main-body {
        padding: 24px;
    }

    /* 设置卡片 */
    .setting-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;
        border: 1px solid #f0f0f0;
    }

    .setting-card h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .setting-card p {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0 0 16px 0;
    }

    /* 表单样式 */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-control:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
    }

    .btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .btn-danger {
        background: var(--danger-color);
        color: white;
    }

    .btn-danger:hover {
        background: #d32f2f;
        color: white;
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }

    .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
    }

    /* 安全信息 */
    .security-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .info-item {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
    }

    .info-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: 4px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .info-value {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 500;
    }

    /* 危险区域 */
    .danger-zone {
        margin-top: 32px;
        padding-top: 24px;
        border-top: 2px solid #ff4d4f;
    }

    .danger-zone h4 {
        color: var(--danger-color);
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .danger-zone p {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 16px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .settings-container {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .form-row {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .security-info {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">账户设置</h1>
    <p class="page-subtitle">管理您的账户信息和安全设置</p>
</div>

<div class="settings-container">
    <!-- 设置侧边栏 -->
    <div class="settings-sidebar">
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="#basic" class="nav-link active" onclick="showSection('basic', this)">
                    <i class="fas fa-user"></i>
                    基本信息
                </a>
            </li>
            <li class="nav-item">
                <a href="#security" class="nav-link" onclick="showSection('security', this)">
                    <i class="fas fa-shield-alt"></i>
                    安全设置
                </a>
            </li>
            <li class="nav-item">
                <a href="#sessions" class="nav-link" onclick="showSection('sessions', this)">
                    <i class="fas fa-desktop"></i>
                    会话管理
                </a>
            </li>
        </ul>
    </div>
    
    <!-- 主内容区 -->
    <div class="settings-main">
        <!-- 基本信息 -->
        <div id="basic-section" class="setting-section">
            <div class="main-header">
                <h3>基本信息</h3>
            </div>
            
            <div class="main-body">
                <div class="setting-card">
                    <h4>账户信息</h4>
                    <p>更新您的基本账户信息</p>
                    
                    <form id="basicForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" name="username" class="form-control" value="<?php echo htmlentities((string) $admin['username']); ?>" required>
                                <div class="form-text">用户名用于登录系统</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" name="name" class="form-control" value="<?php echo htmlentities((string) $admin['name']); ?>" required>
                                <div class="form-text">您的真实姓名或显示名称</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" name="email" class="form-control" value="<?php echo htmlentities((string) $admin['email']); ?>">
                            <div class="form-text">用于接收系统通知和找回密码</div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div id="security-section" class="setting-section" style="display: none;">
            <div class="main-header">
                <h3>安全设置</h3>
            </div>
            
            <div class="main-body">
                <!-- 安全信息 -->
                <div class="security-info">
                    <div class="info-item">
                        <div class="info-label">账户状态</div>
                        <div class="info-value"><?php echo htmlentities((string) $securitySettings['account_status']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">创建时间</div>
                        <div class="info-value"><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($securitySettings['account_created'])? strtotime($securitySettings['account_created']) : $securitySettings['account_created'])); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后登录</div>
                        <div class="info-value"><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($securitySettings['last_login_time'])? strtotime($securitySettings['last_login_time']) : $securitySettings['last_login_time'])); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">登录IP</div>
                        <div class="info-value"><?php echo htmlentities((string) $securitySettings['last_login_ip']); ?></div>
                    </div>
                </div>
                
                <div class="setting-card">
                    <h4>修改密码</h4>
                    <p>定期更改密码以保护您的账户安全</p>
                    
                    <form id="passwordForm">
                        <div class="form-group">
                            <label class="form-label">当前密码</label>
                            <input type="password" name="current_password" class="form-control" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">新密码</label>
                                <input type="password" name="new_password" class="form-control" minlength="6" required>
                                <div class="form-text">密码长度至少6位</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认新密码</label>
                                <input type="password" name="confirm_password" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i>
                                修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 会话管理 -->
        <div id="sessions-section" class="setting-section" style="display: none;">
            <div class="main-header">
                <h3>会话管理</h3>
            </div>
            
            <div class="main-body">
                <div class="setting-card">
                    <h4>活动会话</h4>
                    <p>管理您的登录会话，确保账户安全</p>
                    
                    <div class="security-info">
                        <div class="info-item">
                            <div class="info-label">当前会话</div>
                            <div class="info-value">
                                <i class="fas fa-circle text-success"></i>
                                活跃中
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">会话开始</div>
                            <div class="info-value"><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($securitySettings['last_login_time'])? strtotime($securitySettings['last_login_time']) : $securitySettings['last_login_time'])); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">IP地址</div>
                            <div class="info-value"><?php echo htmlentities((string) $securitySettings['last_login_ip']); ?></div>
                        </div>
                    </div>
                </div>
                
                <!-- 危险区域 -->
                <div class="danger-zone">
                    <h4>危险操作</h4>
                    <p>以下操作将影响您的账户安全，请谨慎操作</p>
                    
                    <div class="setting-card">
                        <h4>清除所有会话</h4>
                        <p>这将强制退出所有设备上的登录状态，您需要重新登录</p>
                        
                        <button type="button" class="btn btn-danger" onclick="clearAllSessions()">
                            <i class="fas fa-sign-out-alt"></i>
                            清除所有会话
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 切换设置区域
    function showSection(sectionName, element) {
        // 隐藏所有区域
        document.querySelectorAll('.setting-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // 显示选中的区域
        document.getElementById(sectionName + '-section').style.display = 'block';
        
        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        element.classList.add('active');
    }

    // 基本信息表单提交
    document.getElementById('basicForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        fetch('/account/updateBasic', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('更新失败，请稍后重试', 'error');
        });
    });

    // 密码修改表单提交
    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // 验证密码确认
        if (data.new_password !== data.confirm_password) {
            showToast('两次输入的密码不一致', 'error');
            return;
        }
        
        fetch('/account/changePassword', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                document.getElementById('passwordForm').reset();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('密码修改失败，请稍后重试', 'error');
        });
    });

    // 清除所有会话
    function clearAllSessions() {
        if (!confirm('确定要清除所有会话吗？这将强制退出所有设备上的登录状态，您需要重新登录。')) {
            return;
        }
        
        fetch('/account/clearSessions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('操作失败，请稍后重试', 'error');
        });
    }

    // Toast 通知
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }
</script>

<!-- Toast 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .text-success {
        color: #52c41a !important;
    }
</style>


        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // 点击外部关闭移动端侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('expanded');
                } else {
                    mainContent.classList.remove('expanded');
                }
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });

        // 设置导航激活状态
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    
</body>
</html>
