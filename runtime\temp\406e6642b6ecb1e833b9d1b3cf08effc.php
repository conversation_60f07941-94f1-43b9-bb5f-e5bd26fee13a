<?php /*a:2:{s:41:"F:\linshi\thphp\kmxt\view\card\index.html";i:1753951970;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1753955066;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --sidebar-width: 240px;
            --header-height: 64px;
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --sidebar-bg: #001529;
            --sidebar-text: rgba(255, 255, 255, 0.65);
            --sidebar-active: #1890ff;
            --content-bg: #f0f2f5;
            --card-bg: #ffffff;
            --border-color: #d9d9d9;
            --text-primary: #262626;
            --text-secondary: #8c8c8c;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-header {
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            color: var(--sidebar-text);
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            padding: 0 24px;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .nav-link {
            color: var(--sidebar-text);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .nav-link i {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
            background-color: var(--content-bg);
            padding: 24px;
        }

        .main-content.expanded {
            margin-left: 0;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<style>
    .filter-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
    }

    .table-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .btn-group .btn {
        margin-right: 8px;
    }

    .table {
        margin: 0;
    }

    .table th {
        background-color: var(--content-bg);
        border: none;
        font-weight: 600;
        color: var(--text-primary);
        padding: 16px 12px;
        font-size: 14px;
    }

    .table td {
        border: none;
        padding: 16px 12px;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        font-size: 14px;
    }

    .table tbody tr:hover {
        background-color: var(--content-bg);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-unused {
        background-color: #fff7e6;
        color: #d46b08;
        border: 1px solid #ffd591;
    }

    .status-used {
        background-color: #f6ffed;
        color: #389e0d;
        border: 1px solid #b7eb8f;
    }

    .status-disabled {
        background-color: #fff2f0;
        color: #cf1322;
        border: 1px solid #ffccc7;
    }
    
    .card-code {
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        background-color: var(--content-bg);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid var(--border-color);
    }

    .pagination {
        justify-content: center;
        margin-top: 24px;
    }
    
    .filter-form .form-control,
    .filter-form .form-select {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .table-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .btn-group {
            flex-wrap: wrap;
        }
        
        .filter-form {
            flex-direction: column;
        }
        
        .filter-form .form-control,
        .filter-form .form-select {
            margin-right: 0;
            width: 100%;
        }
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 16px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .modal-header h2 i {
        margin-right: 12px;
        color: rgba(255, 255, 255, 0.9);
    }

    .close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.2s ease;
        line-height: 1;
    }

    .close:hover {
        color: white;
    }

    .modal-body {
        padding: 32px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 20px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        text-align: right;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    /* 生成说明样式 */
    .generate-tips {
        background: #f0f9ff;
        border-left: 4px solid var(--primary-color);
        padding: 16px;
        border-radius: 0 6px 6px 0;
        margin-bottom: 24px;
        border: 1px solid #bae7ff;
        border-left: 4px solid var(--primary-color);
    }

    .generate-tips h6 {
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
    }

    .generate-tips ul {
        margin: 0;
        padding-left: 20px;
    }

    .generate-tips li {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 4px;
        line-height: 1.5;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .modal .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .modal .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 分类层级样式 */
    .category-option-level-1 {
        font-weight: 700;
        color: #1890ff !important;
        background-color: #f0f9ff !important;
    }

    .category-option-level-2 {
        color: #52c41a !important;
        font-weight: 600;
        background-color: #f6ffed !important;
    }

    .category-option-level-3 {
        color: #fa8c16 !important;
        font-weight: 500;
        background-color: #fff7e6 !important;
        font-size: 13px;
    }

    /* 按钮样式 */
    .btn-primary, .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .btn-primary:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    /* Toast 动画 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modal-content {
            margin: 10% auto;
            width: 95%;
            max-height: 85vh;
        }

        .modal-header {
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            flex-direction: column;
        }
    }
</style>

</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
                <a href="/logs" class="nav-link">
                    <i class="fas fa-list-alt"></i>
                    操作日志
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<div class="page-title">
    <h1>卡密管理</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/dashboard">控制台</a></li>
            <li class="breadcrumb-item active">卡密管理</li>
        </ol>
    </nav>
</div>

<!-- 筛选条件 -->
<div class="filter-card">
    <form class="filter-form d-flex flex-wrap align-items-end" method="get">
        <div class="me-3 mb-2">
            <label class="form-label">状态</label>
            <select name="status" class="form-select">
                <option value="">全部状态</option>
                <option value="0" <?php echo $filters['status']=='0' ? 'selected'  :  ''; ?>>未使用</option>
                <option value="1" <?php echo $filters['status']=='1' ? 'selected'  :  ''; ?>>已使用</option>
                <option value="2" <?php echo $filters['status']=='2' ? 'selected'  :  ''; ?>>已禁用</option>
            </select>
        </div>
        
        <div class="me-3 mb-2">
            <label class="form-label">分类</label>
            <select name="category_id" class="form-select">
                <option value="">全部分类</option>
                <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                <option value="<?php echo htmlentities((string) $category['id']); ?>" <?php echo $filters['category_id']==$category['id'] ? 'selected'  :  ''; ?>><?php echo htmlentities((string) $category['name']); ?></option>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </select>
        </div>
        
        <div class="me-3 mb-2">
            <label class="form-label">卡密编号</label>
            <input type="text" name="keyword" class="form-control" placeholder="搜索卡密编号" value="<?php echo htmlentities((string) $filters['keyword']); ?>">
        </div>
        
        <div class="mb-2">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
            </button>
            <a href="/cards" class="btn btn-outline-secondary">
                <i class="fas fa-refresh"></i> 重置
            </a>
        </div>
    </form>
</div>

<!-- 卡密列表 -->
<div class="table-card">
    <div class="table-header">
        <h5 class="table-title">卡密列表</h5>
        <div class="btn-group">
            <button type="button" class="btn btn-success" onclick="showGenerateModal()">
                <i class="fas fa-plus"></i> 生成卡密
            </button>
            <button type="button" class="btn btn-warning" onclick="batchAction('disable')">
                <i class="fas fa-ban"></i> 批量禁用
            </button>
            <button type="button" class="btn btn-danger" onclick="batchAction('delete')">
                <i class="fas fa-trash"></i> 批量删除
            </button>
            <button type="button" class="btn btn-info" onclick="showExportModal()">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th width="50">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    </th>
                    <th>卡密编号</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>内容</th>
                    <th>创建时间</th>
                    <th>使用时间</th>
                    <th>过期时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(is_array($cards) || $cards instanceof \think\Collection || $cards instanceof \think\Paginator): $i = 0; $__LIST__ = $cards;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$card): $mod = ($i % 2 );++$i;?>
                <tr>
                    <td>
                        <input type="checkbox" class="card-checkbox" value="<?php echo htmlentities((string) $card['id']); ?>">
                    </td>
                    <td>
                        <code class="card-code"><?php echo htmlentities((string) $card['masked_card_code']); ?></code>
                    </td>
                    <td><?php echo htmlentities((string) $card['category_name']); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo $card['status']==0 ? 'unused'  :  ($card['status'] == 1 ? 'used' : 'disabled'); ?>">
                            <?php echo htmlentities((string) $card['status_text']); ?>
                        </span>
                    </td>
                    <td>
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?php echo htmlentities((string) $card['content']); ?>">
                            <?php echo htmlentities((string) $card['content']); ?>
                        </div>
                    </td>
                    <td><?php echo htmlentities((string) $card['created_at']); ?></td>
                    <td><?php echo !empty($card['used_at']) ? htmlentities((string) $card['used_at']) : '-'; ?></td>
                    <td><?php echo !empty($card['expire_at']) ? htmlentities((string) $card['expire_at']) : '-'; ?></td>
                    <td>
                        <?php if($card['status'] != 1): ?>
                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="updateStatus(<?php echo htmlentities((string) $card['id']); ?>, <?php echo $card['status']==0 ? 2  :  0; ?>)">
                            <?php echo $card['status']==0 ? '禁用'  :  '启用'; ?>
                        </button>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; endif; else: echo "" ;endif; if(empty($cards) || (($cards instanceof \think\Collection || $cards instanceof \think\Paginator ) && $cards->isEmpty())): ?>
                <tr>
                    <td colspan="9" class="text-center text-muted py-4">暂无数据</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php echo htmlentities((string) $cards->render()); ?>
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出卡密</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportStatus" class="form-label">状态筛选</label>
                        <select class="form-select" id="exportStatus">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                            <option value="2">已禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exportCategory" class="form-label">分类筛选</label>
                        <select class="form-select" id="exportCategory">
                            <option value="">全部分类</option>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $category['id']); ?>"><?php echo htmlentities((string) $category['name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportCards()">导出</button>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // 点击外部关闭移动端侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('expanded');
                } else {
                    mainContent.classList.remove('expanded');
                }
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });

        // 设置导航激活状态
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    
<script>
    // 全选/取消全选
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.card-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
    }
    
    // 更新卡密状态
    function updateStatus(id, status) {
        if (!confirm('确定要修改此卡密的状态吗？')) {
            return;
        }
        
        fetch('/cards/updateStatus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id, status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('状态更新成功');
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 批量操作
    function batchAction(action) {
        const checkboxes = document.querySelectorAll('.card-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('请选择要操作的卡密');
            return;
        }

        const ids = Array.from(checkboxes).map(cb => cb.value);
        const actionText = action === 'delete' ? '删除' : '禁用';

        if (!confirm(`确定要${actionText}选中的 ${ids.length} 个卡密吗？`)) {
            return;
        }

        const url = '/cards/batchDelete';

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('操作失败，请稍后重试');
        });
    }
    
    // 显示导出模态框
    function showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }
    
    // 导出卡密
    function exportCards() {
        const password = document.getElementById('exportPassword').value;
        const status = document.getElementById('exportStatus').value;
        const categoryId = document.getElementById('exportCategory').value;
        
        if (!password) {
            alert('请输入导出密码');
            return;
        }
        
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/cards/export';
        
        const fields = {
            password: password,
            status: status,
            category_id: categoryId
        };
        
        for (const key in fields) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = fields[key];
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();
    }

    // 显示生成卡密模态框
    function showGenerateModal() {
        // 先获取分类数据
        fetch('/cards/getCategories')
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    populateCategories(data.data);
                    document.getElementById('generateModal').style.display = 'block';
                    document.body.style.overflow = 'hidden';

                    // 设置默认过期时间为30天后
                    setDefaultExpireTime();
                } else {
                    showToast('获取分类数据失败', 'error');
                }
            })
            .catch(error => {
                showToast('获取分类数据失败', 'error');
            });
    }

    // 关闭生成卡密模态框
    function closeGenerateModal() {
        document.getElementById('generateModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        document.getElementById('generateForm').reset();
    }

    // 填充分类选项
    function populateCategories(categories) {
        const select = document.getElementById('generate_category_id');
        select.innerHTML = '<option value="">请选择分类</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.className = `category-option-level-${category.level}`;

            let prefix = '';
            switch(category.level) {
                case 1:
                    prefix = '■ ';
                    break;
                case 2:
                    prefix = '    ▶ ';
                    break;
                case 3:
                    prefix = '        ● ';
                    break;
            }

            option.textContent = prefix + category.name;
            select.appendChild(option);
        });
    }

    // 设置默认过期时间
    function setDefaultExpireTime() {
        const expireInput = document.getElementById('generate_expire_at');
        const now = new Date();
        now.setDate(now.getDate() + 30);

        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        expireInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 提交生成表单
    function submitGenerate() {
        const form = document.getElementById('generateForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // 验证表单
        if (!data.category_id || !data.count || !data.content) {
            showToast('请填写所有必填项', 'warning');
            return;
        }

        const count = parseInt(data.count);
        if (count < 1 || count > 1000) {
            showToast('生成数量必须在1-1000之间', 'warning');
            return;
        }

        // 显示加载状态
        setGenerateLoading(true);

        // 发送请求
        fetch('/cards/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showToast(result.message, 'success');
                closeGenerateModal();
                // 刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(result.message, 'error');
            }
        })
        .catch(error => {
            showToast('生成失败，请稍后重试', 'error');
        })
        .finally(() => {
            setGenerateLoading(false);
        });
    }

    // 设置生成按钮加载状态
    function setGenerateLoading(loading) {
        const btn = document.getElementById('generateSubmitBtn');
        const btnText = btn.querySelector('.btn-text');
        const loadingSpinner = btn.querySelector('.loading-spinner');

        if (loading) {
            btnText.style.display = 'none';
            loadingSpinner.style.display = 'inline-block';
            btn.disabled = true;
        } else {
            btnText.style.display = 'inline-block';
            loadingSpinner.style.display = 'none';
            btn.disabled = false;
        }
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const generateModal = document.getElementById('generateModal');
        if (event.target === generateModal) {
            closeGenerateModal();
        }
    }
</script>

<!-- 生成卡密模态框 -->
<div id="generateModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2><i class="fas fa-magic"></i> 生成卡密</h2>
            <span class="close" onclick="closeGenerateModal()">&times;</span>
        </div>
        <div class="modal-body">
            <!-- 生成说明 -->
            <div class="generate-tips">
                <h6><i class="fas fa-info-circle"></i> 生成说明</h6>
                <ul>
                    <li>每次最多可生成1000个卡密</li>
                    <li>卡密编号将自动生成，确保唯一性</li>
                    <li>过期时间为可选项，不设置则永不过期</li>
                    <li>生成后的卡密状态为"未使用"</li>
                </ul>
            </div>

            <!-- 生成表单 -->
            <form id="generateForm">
                <div class="form-group">
                    <label for="generate_category_id">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <select id="generate_category_id" name="category_id" class="form-control" required>
                        <option value="">请选择分类</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="generate_count">生成数量 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="generate_count" name="count" class="form-control"
                           min="1" max="1000" value="10" required>
                    <small class="form-text">请输入1-1000之间的数字</small>
                </div>

                <div class="form-group">
                    <label for="generate_content">兑换内容 <span style="color: #ff4d4f;">*</span></label>
                    <textarea id="generate_content" name="content" class="form-control"
                              rows="4" placeholder="请输入卡密兑换后显示的内容..." required></textarea>
                    <small class="form-text">用户兑换卡密后将看到此内容</small>
                </div>

                <div class="form-group">
                    <label for="generate_expire_at">过期时间</label>
                    <input type="datetime-local" id="generate_expire_at" name="expire_at" class="form-control">
                    <small class="form-text">不设置则永不过期</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeGenerateModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="generateSubmitBtn" class="btn-primary" onclick="submitGenerate()">
                <span class="btn-text">
                    <i class="fas fa-magic"></i> 开始生成
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 生成中...
                </span>
            </button>
        </div>
    </div>
</div>


</body>
</html>
